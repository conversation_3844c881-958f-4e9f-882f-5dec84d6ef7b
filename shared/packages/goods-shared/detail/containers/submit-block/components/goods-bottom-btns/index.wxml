<template name="basic-btn">
  <theme-view
    bg="{{ isMain ? 'main-bg' : 'vice-bg' }}"
    color="{{ isMain ? 'main-text' : 'vice-text' }}"
    gradient-start="{{ btnGradient.start }}"
    gradient-end="{{ btnGradient.end }}"
    gradient-deg="{{ btnGradient.deg }}"
    gradient="{{ btnGradient.be }}"
  >
    <button
      open-type="{{ btn.share ? 'share' : ''}}"
      class="goods-action-btn {{ btn.disabled ? 'goods-buttons--disabled' : ''}}"
    >
      <view
        wx:if="{{ btn.leftText }}"
        class="goods-buttons__big-col {{ btn.leftText.length > 10 ? 'goods-buttons__font-sm' : '' }}"
      >
        <view class="{{ !btn.leftSub ? 'goods-buttons__font-xl': '' }}">
          {{ btn.leftText }}
        </view>
        <view wx:if="{{ btn.leftSub }}">{{ btn.leftSub }}</view>
      </view>
      <view class="goods-buttons__big-col">
        <view class="{{ !btn.sub ? 'goods-buttons__font-xl': '' }}">{{ btn.text }}</view>
        <view
          wx:if="{{ btn.sub }}"
          class="{{ btn.sub.length > 12 ? 'goods-buttons__font-s': '' }}"
        >{{ btn.sub }}</view>
      </view>
    </button>
  </theme-view>
</template>

<template name="main-btn">
  <!-- 海淘商品强制用手机号校验 -->
  <user-authorize
    bind:next="bigButtonClick"
    scene="{{ btn.isHaitaoForbiden ? '' : btn.accountUnionScene }}"
    authTypeList="{{ btn.isHaitaoForbiden ? ['mobile'] : [] }}"
    data-btn="{{ btn }}"
    btn-class="goods-buttons__radius"
    data-btn-type="{{ isMain ? 'main' : 'vice'}}"
    allowDeny="{{ true }}"
  >
     <template
      is="basic-btn"
      data="{{ ...{ btn: btn, btnGradient: btnGradient, isMain: isMain } }}"
    >
  </template>
  </user-authorize>
</template>

<template name="main-subscribe-btn">
  <subscribe-message
    bind:next="bigButtonClick"
    scene="{{ btn.accountUnionScene }}"
    data-btn="{{ btn }}"
    btn-class="goods-buttons__radius"
    subscribe-scene="{{ btn.subscribeScene }}"
    data-btn-type="{{ isMain ? 'main' : 'vice'}}"
  >
    <theme-view
      bg="{{ isMain ? 'main-bg' : 'vice-bg' }}"
      color="{{ isMain ? 'main-text' : 'vice-text' }}"
      gradient-start="{{ btnGradient.start }}"
      gradient-end="{{ btnGradient.end }}"
      gradient-deg="{{ btnGradient.deg }}"
      gradient="{{ btnGradient.be }}"
    >
      <button
        open-type="{{ btn.share ? 'share' : ''}}"
        class="goods-action-btn {{ btn.disabled ? 'goods-buttons--disabled' : ''}}"
      >
        <view
          wx:if="{{ btn.leftText }}"
          class="goods-buttons__big-col {{ btn.leftText.length > 10 ? 'goods-buttons__font-sm' : '' }}"
        >
          <view class="{{ !btn.leftSub ? 'goods-buttons__font-xl': '' }}">
            {{ btn.leftText }}
          </view>
          <view wx:if="{{ btn.leftSub }}">{{ btn.leftSub }}</view>
        </view>
        <view class="goods-buttons__big-col">
          <view class="{{ !btn.sub ? 'goods-buttons__font-xl': '' }}">{{ btn.text }}</view>
          <view
            wx:if="{{ btn.sub }}"
            class="{{ btn.sub.length > 12 ? 'goods-buttons__font-s': '' }}"
          >{{ btn.sub }}</view>
        </view>
      </button>
    </theme-view>
  </subscribe-message>
</template>

<template name="new-main-subscribe-btn">
  <subscribe-message-v2
    bind:next="bigButtonClick"
    bind:success="handleWxSubscribeSuccess"
    bind:fail="handleWxSubScribeFail"
    btn-class="goods-buttons__radius"
    subscribe-scene="{{ btn.subscribeSceneV2 }}"
    data-btn="{{ btn }}"
    data-btn-type="{{ isMain ? 'main' : 'vice'}}"
  >
    <theme-view
      bg="{{ isMain ? 'main-bg' : 'vice-bg' }}"
      color="{{ isMain ? 'main-text' : 'vice-text' }}"
      gradient-start="{{ btnGradient.start }}"
      gradient-end="{{ btnGradient.end }}"
      gradient-deg="{{ btnGradient.deg }}"
      gradient="{{ btnGradient.be }}"
    >
      <button
        open-type="{{ btn.share ? 'share' : ''}}"
        class="goods-action-btn {{ btn.disabled ? 'goods-buttons--disabled' : ''}}"
      >
        <view
          wx:if="{{ btn.leftText }}"
          class="goods-buttons__big-col"
        >
          <view class="{{ !btn.leftSub ? 'goods-buttons__font-xl': '' }}">
            {{ btn.leftText }}
          </view>
          <view wx:if="{{ btn.leftSub }}">{{ btn.leftSub }}</view>
        </view>
        <view class="goods-buttons__big-col">
          <view class="{{ !btn.sub ? 'goods-buttons__font-xl': '' }}">{{ btn.text }}</view>
          <view
            wx:if="{{ btn.sub }}"
            class="goods-buttons__font--s"
          >{{ btn.sub }}</view>
        </view>
      </button>
    </theme-view>
  </subscribe-message-v2>
</template>

<template name="main-limit-guide-btn">
  <!-- 海淘商品强制用手机号校验 -->
  <user-authorize
    wx:if="{{!isMobileAuthorized}}"
    bind:next="handleAuthorize"
    authTypeList="{{ ['mobile'] }}"
    data-btn="{{ btn }}"
    btn-class="goods-buttons__radius"
    data-btn-type="{{ isMain ? 'main' : 'vice'}}"
  >
    <template
        is="basic-btn"
        data="{{ ...{ btn: btn, btnGradient: btn.primary ? mainGradient : viceGradient, isMain: btn.primary } }}"
      >
    </template>
  </user-authorize>
  <view
    wx:else 
    class="goods-buttons__radius"
    data-btn="{{ btn }}"
    bindtap="bigButtonClick">
    <template
      is="basic-btn"
      data="{{ ...{ btn: btn, btnGradient: btn.primary ? mainGradient : viceGradient, isMain: btn.primary } }}"
    >
    </template>
  </view>
</template>

<view class="goods-buttons">
  <view class="goods-buttons__mini">
    <goods-action-icon
      wx:for="{{ miniButtonsData }}"
      wx:for-item="btn"
      wx:key="type"
      data-btn="{{ btn }}"
      class="icon-btn__view"
      type="{{ btn.type }}"
      icon="{{ btn.icon }}"
      text="{{ btn.text }}"
      theme="{{ btn.theme }}"
      icon-info="{{ btn.info }}"
      bind:tap="miniButtonClick"
    />
    <goods-plus-btns
      wx:if="{{ plusButtonsData.length > 0 }}"
      buttons="{{ plusButtonsData }}"
    />
  </view>
  <!-- 显示秒杀库存 pop 框 -->
  <limited-kill-pop wx:if="{{ isDisplayLimitedSeckillPop }}" class="litited_kill_pop" />
  <view
    wx:for="{{ bigButtons }}"
    wx:for-item="btn"
    wx:key="skuScene"
    class="goods-buttons__big goods-buttons__big-{{ index === 0 ? 'first' : '' }}"
  >
  <template
    wx:if="{{ btn.subscribeScene }}"
    is="main-subscribe-btn"
    data="{{ ...{ btn: btn, btnGradient: btn.primary ? mainGradient : viceGradient, isMain: btn.primary } }}"
  >
  </template>
  <template
    wx:elif="{{ btn.subscribeSceneV2 }}"
    is="new-main-subscribe-btn"
    data="{{ ...{ btn: btn, btnGradient: btn.primary ? mainGradient : viceGradient, isMain: btn.primary } }}"
  >
  </template>
  <template
    wx:elif="{{ btn.isLimitBuyGuide }}"
    is="main-limit-guide-btn"
    data="{{ ...{ btn: btn, btnGradient: btn.primary ? mainGradient : viceGradient, isMain: btn.primary, isMobileAuthorized: isMobileAuthorized } }}"
  >
  </template>
  <template
    wx:else
    is="main-btn"
    data="{{ ...{ btn: btn, isMain: btn.primary }, btnGradient: btn.primary ? mainGradient : viceGradient }}"
  >
  </template>
  </view>
</view>
