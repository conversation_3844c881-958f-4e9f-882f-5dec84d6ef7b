import { jumpLink } from '@goods/utils';
import Toast from '@vant/weapp/dist/toast/toast';
import { doSubmitLog } from '@goods/common/submit-helper';
import { SKU_SCENE, WX_SUBSCRIBE_SCENE } from '@goods/common/config';
import api from '@goods/utils/api';
import { setLogger, getSkuLogData } from '@goods/common/logger';
import pick from '@youzan/weapp-utils/lib/pick';
import {
  PAGE_TYPE,
  navigateToRantaPage,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import noneSkuToOrderGoodsData from '@goods/common/none-sku-order-goods-data';
import openWebView from 'shared/utils/open-web-view';
import Theme from 'shared/common/components/theme-view/theme';

export default {
  // 大按钮点击埋点、统一上报活动信息
  onBtnClickLog({ state, getters }, btn) {
    if (btn.skuScene === SKU_SCENE.POINTS_BUY) {
      setLogger({
        et: 'click', // 事件类型
        ei: 'click_exchange_goods_by_score_button', // 事件标识
        en: '点击积分兑换商品按钮', // 事件名称
        params: {
          goods_id: getters.goodsId,
          goods_name: state.goods.title,
          ...getSkuLogData({
            skuExtraData: state.skuExtraData,
            originSku: state.originSku,
          }),
        }, // 事件参数
      });
    }
    const { currentActivity, isDepositPresale } = getters;

    if (!currentActivity) {
      return;
    }

    if (
      // 活动购买、砍价
      [SKU_SCENE.ACT_BUY, SKU_SCENE.START_CUT].indexOf(btn.skuScene) === -1 &&
      // 定金膨胀
      !isDepositPresale
    ) {
      return;
    }

    const params = pick(currentActivity, 'activityId', 'type', 'activityName');

    // 定金膨胀参数额外处理
    if (isDepositPresale) {
      params.type = 'depositPresale';
    }

    // 活动点击埋点
    setLogger({
      et: 'click', // 事件类型
      ei: 'activity_click', // 事件标识
      en: '活动按钮点击', // 事件名称
      params,
    });
  },
  // 提交客服咨询日志
  submitImLog({ state }) {
    // 日志埋点
    const { goods = {}, sku = {} } = state;
    doSubmitLog('im', {
      goods,
      sku,
    });
  },

  doAppShare() {
    //
  },

  // 小按钮按钮点击
  handleMiniButtonClick({ commit, dispatch, state }, btn = {}) {
    const { url, type } = btn;
    const { accountUnionScene: ACCOUNT_UNION_SCENE = {} } =
      state.businessConfigs;
    // 改成自行跳转
    if (url) {
      if (type === 'cart') {
        navigateToRantaPage({
          url,
          pageType: PAGE_TYPE.CART,
        });
      } else {
        jumpLink(url);
      }
    }

    switch (type) {
      case 'im':
        dispatch('submitImLog');
        break;
      case 'shop':
        if (url) {
          // 拦截首页链接
          jumpLink(url);
        }
        break;
      case 'cart':
        setLogger({
          et: 'click', // 事件类型
          ei: 'enter_cart', // 事件标识
          en: '进入购物车',
        });
        break;
      case 'share':
        // 微小店专用
        dispatch('doAppShare');
        break;
      case 'gift':
        dispatch('triggerSku', {
          skuScene: SKU_SCENE.GIFT_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
        });
        break;
      case 'point':
        dispatch('triggerSku', {
          skuScene: SKU_SCENE.POINTS_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.POINTS_BUY,
        });
        break;
      case 'more':
        commit('showMoreMiniBtns', true);
        break;
      default:
        dispatch('handleMiniButtonClickExtra');
        break;
    }
  },

  handleMiniButtonClickExtra() {
    // 虚函数，可以在基础handle之外处理额外事件
  },

  // 大按钮点击
  handleBigButtonClick({ dispatch, getters, commit, state }, btn = {}) {
    // 如预览商品不支持点击按钮购买
    if (getters.forbidClickBtnReason) {
      return dispatch('showCommonToast', getters.forbidClickBtnReason);
    }

    if (btn.disabled) {
      if (btn.skuScene === SKU_SCENE.SALE_REMINDER) {
        Toast({
          message: '开售前会发送小程序订阅消息提醒你',
          position: 'bottom',
        });
      } else if (btn.skuScene === SKU_SCENE.STOCK_REMINDER) {
        Toast({
          message: '商家已收到补货提醒，补货后通知你',
          position: 'bottom',
        });
      }
      return;
    }

    if (btn.url) {
      // 引导办会员
      if (btn.skuScene === SKU_SCENE.LIMIT_GUIDE) {
        dispatch('limitGuideHandle', btn);
        return;
      }
      jumpLink(btn.url);
      return;
    }

    // 触发分享
    if (btn.share) {
      dispatch('handleShareClick');
      return;
    }

    // 处理大按钮点击后的埋点事件
    dispatch('onBtnClickLog', btn);

    // 是否是预约，比如秒杀预约
    if (btn.skuScene === SKU_SCENE.RESERVATION) {
      // 提交vanx dispatch预约
      dispatch('makeReservation');
      return;
    }

    // 开始砍价
    if (btn.skuScene === SKU_SCENE.START_CUT) {
      // if (
      //   get(state, 'marketActivity.helpCut.isFollowStart', false)
      //   && !get(state, 'buyConfig.isFans')
      // ) {
      //   dispatch('doPageFollow');
      //   return;
      // }
      dispatch('startCut');
      return;
    }

    // 内购裂变 && 内购价购买 && 内购劵不足
    if (
      getters.isInSourcingFission &&
      btn.skuScene === SKU_SCENE.ACT_BUY &&
      !getters.isEnoughCoupon
    ) {
      dispatch('handleShareClick');
      return;
    }

    // 新品发售当进入页面时距开售小于5分钟会走到这，可以直接预约
    if (getters.isProductLaunch && btn.skuScene === SKU_SCENE.PRODUCT_LAUNCH) {
      dispatch('productLaunchReservation');
      return;
    }

    // 无规格商品直接下单降级处理
    const { pageSwitch } = state || {};
    const { close_none_sku_goods_jump: close } = pageSwitch;

    // 无规格商品，直接到下单页；
    const { startSaleNum } = getters;
    const { goods = {}, activityInfo = {}, originActivitySku } = state;
    const { stockNum } = goods;
    const { activityType } = activityInfo;

    // 拼团活动中 只有普通拼团(activityType === 4 )点击立即开团需要直接下单
    if (
      getters.isDirectOrder &&
      (btn.skuScene === SKU_SCENE.NORMAL_BUY || activityType === 4) &&
      !close
    ) {
      const payload = noneSkuToOrderGoodsData(
        { goods, originActivitySku, activityInfo },
        { startSaleNum },
        btn
      );
      // 库存小于起购数？
      stockNum < startSaleNum
        ? Toast({
            message: '该商品库存不足',
            position: 'center',
          })
        : // sku场景同时有购物车和购买按钮时，确认不是购物车，就需要把标志位重置
          commit('setSkuConfig', {
            isAddCart: false,
          });
      // 无规格商品埋点；
      setLogger({
        et: 'custom', // 事件类型
        ei: 'appear_or_disappear_sku_buy', // 事件标识
        en: '是否出现立即购买sku面板', // 事件名称
        params: {
          appear_sku_buy: '0',
          component: 'sku',
        }, // 事件参数
      });
      return dispatch('handleBuy', { detail: payload });
    }

    dispatch('triggerSku', btn);
  },

  makeReservation() {
    // 秒杀预约，空函数
  },

  startCut() {
    // 开始砍价，空函数
  },

  // 刷新购物车记录
  refreshCartCount({ state, commit }) {
    if (state.shopConfig.hideShoppingCart) {
      return;
    }
    if (!state.pageSwitch['cart-count']) {
      return;
    }

    api
      .get({
        url: '/wscgoods/detail-api/cart-count.json',
      })
      .then(({ value }) => {
        commit('REFRESH_CART_COUNT', value);
      });
  },

  handleWxSubscribeSuccess({ getters, dispatch }, { detail = {} }) {
    switch (detail.scene) {
      case WX_SUBSCRIBE_SCENE.SALE_REMINDER:
        // 新品开售 && 商品未上架
        if (detail.success && getters.isProductLaunch) {
          dispatch('productLaunchReservation');
        } else {
          dispatch('handleSaleReminderSuccess', detail.success);
        }
        break;
      case WX_SUBSCRIBE_SCENE.STOCK_REMINDER:
        dispatch('handleStockReminderSuccess', detail.success);
        break;
      case WX_SUBSCRIBE_SCENE.UMP_SECKILL_BOOK:
        dispatch('bookSeckill');
        break;
      default:
        break;
    }
  },

  handleWxSubScribeFail({ dispatch }, { detail = {} }) {
    switch (detail.scene) {
      case WX_SUBSCRIBE_SCENE.SALE_REMINDER:
        dispatch('handleSaleReminderFail', detail.err);
        break;
      case WX_SUBSCRIBE_SCENE.STOCK_REMINDER:
        dispatch('handleStockReminderFail', detail.err);
        break;
      default:
        break;
    }
  },
  // 引导办会员跳转
  limitGuideHandle({ state }, btn = {}) {
    // 免费会员只有 h5
    const { buyLimitGuideInfo = {} } = state.goods;
    const { guideType } = buyLimitGuideInfo;
    const { url } = btn;

    // 打点
    let ei = '';
    let en = '';
    switch (guideType) {
      case 1:
        ei = 'click_button_free';
        en = '商详页_限购按钮点击_免费会员';
        break;
      case 2:
        ei = 'click_button_pay';
        en = '商详页_限购按钮点击_付费会员';
        break;
      case 3:
      case 4:
        ei = 'click_button_card';
        en = '商详页_限购按钮点击_权益卡';
        break;
      default:
        break;
    }
    setLogger({
      et: 'click',
      ei,
      en,
      params: {},
    });

    if (!/^\/packages/.test(url)) {
      openWebView(url);
    } else {
      jumpLink(url);
    }
  },

  getThemeType({ commit }) {
    Theme.getThemeType().then((type) => {
      commit('SET_THEME_TYPE', type);
    });
  },
};
