import { SKU_SCENE, WX_SUBSCRIBE_SCENE } from '@goods/common/config';
import args from '@youzan/weapp-utils/lib/args';
import { setLogger } from '@goods/common/logger';

export default {
  // 如果有底部提示条，倒计时不展示；
  showBottomCountdown(state, getters) {
    const {
      shopRisk,
      isGroupOn,
      waitToSoldCountdown,
      goodsBottomBarTip,
      showAvailableStores,
      hideAvailableStores,
    } = getters;
    return !(
      shopRisk.match ||
      isGroupOn ||
      waitToSoldCountdown ||
      goodsBottomBarTip.text ||
      (showAvailableStores && !hideAvailableStores)
    );
  },
  // 店铺风险提示
  // { match, note }
  shopRisk(state) {
    return (
      state.goods.risk || {
        match: false,
        note: '',
      }
    );
  },
  // 倒计时 复用base-store的
  // countdown

  // 参与f码活动的商品售罄，会引导去f码通道购买
  isSupportFCodeBuy(state, getters) {
    const {
      goods: { isSupportFCode, stockNum },
      display: { showForbidBuyBtn },
    } = state;

    if (getters.isTimelimitedDiscount) {
      return false;
    }

    return isSupportFCode && showForbidBuyBtn && stockNum === 0;
  },

  // 底部不可购买展示内容
  goodsBottomDisabledTip(state) {
    const { forbidBuyReason = '' } = state.display;
    const { stockNum = 0, isDisplay = 1 } = state.goods;
    // 商品下架
    if (isDisplay !== 1) {
      return {
        text: '商品下架啦',
        url: '',
      };
    }
    // 如果没有库存，会有其他提示条(精选推荐，recommend-block)
    if (!stockNum) {
      return {
        text: '',
        url: '',
      };
    }
    return {
      text: forbidBuyReason,
      url: '',
    };
  },

  // 按钮上方提示条
  // 特定活动会重写
  goodsBottomBarTip(state, getters) {
    return getters.goodsBottomDisabledTip;
  },

  viceGradient(state, getters, rootState) {
    const { theme = {} } = rootState.shop || {};
    const gradient = {};
    const themeType = theme.type || state.themeType;

    if (themeType === 13) {
      gradient.be = true;
      gradient.start = '#ffd01e';
      gradient.end = '#ff8917';
      gradient.deg = '90';
    }
    return gradient;
  },

  mainGradient(state, getters, rootState) {
    const { theme = {} } = rootState.shop || {};
    const themeType = theme.type || state.themeType;
    const gradient = {};

    if (themeType === 13) {
      gradient.be = true;
      gradient.start = '#ff6034';
      gradient.end = '#ee0a24';
      gradient.deg = '90';
    }
    return gradient;
  },

  // 小按钮配置
  miniBtnsConfig(state, getters) {
    const { pointsName = '积分' } = state.goods;
    return {
      im: {
        text: getters.bottomImBtnTxt,
        icon: 'chat-o',
        type: 'im',
        theme: true,
        className: 'js-im-icon theme__icon',
      },
      shop: {
        text: '店铺',
        icon: 'shop-o',
        type: 'shop',
        url: getters.homepageUrl,
      },
      share: {
        text: '推广',
        icon: 'share',
        type: 'share',
      },
      cart: {
        text: getters.cartText,
        icon: 'shopping-cart-o',
        type: 'cart',
        info: state.cartInfo.count || '',
        url: getters.cartUrl,
      },
      gift: {
        text: '送人',
        icon: 'gift-o',
        type: 'gift',
      },
      point: {
        text: pointsName,
        icon: 'points',
        type: 'point',
      },
      more: {
        text: '更多',
        icon: 'more-o',
        type: 'more',
      },
      activity: {
        text: '进活动',
        icon: 'point-gift-o',
        type: 'activity',
        url: getters.activityUrl,
      },
    };
  },

  miniBtnsMap(state) {
    if (Object.keys(state.miniButtons).length) {
      return state.miniButtons;
    }

    const {
      showGiftBtn,
      showMiniShopBtn,
      showMiniCartBtn,
      showMiniPointsBtn,
      showShareBtn,
      showImBtn,
      showForbidBuyBtn,
      // 是否显示进活动小按钮
      showMiniActivityButton,
    } = state.display;
    return {
      im: showImBtn,
      shop: showMiniShopBtn,
      cart: showMiniCartBtn,
      share: showShareBtn && !showForbidBuyBtn,
      gift: showGiftBtn && !showForbidBuyBtn,
      point: showMiniPointsBtn && !showForbidBuyBtn,
      activity: showMiniActivityButton,
    };
  },

  // 小按钮平铺最多展示数 / 是否展示更多小按钮
  plusBtnConfig(state, getters) {
    const { bigButtonsData, totalMiniButtons } = getters;
    const bigLen = bigButtonsData.length;
    const miniLen = totalMiniButtons.length;

    if (bigLen === 2 && miniLen > 3) {
      return {
        maxMiniCount: 3,
        showMoreBtn: true,
      };
    }
    if (bigLen === 1 && miniLen > 4) {
      return {
        maxMiniCount: 4,
        showMoreBtn: true,
      };
    }

    return {
      maxMiniCount: 8,
      showMoreBtn: false,
    };
  },

  totalMiniButtons(state, getters) {
    const { miniBtnsMap } = getters;

    // 外链商品只显示客服按钮
    if (state.goods.isOutlink) {
      return [];
    }

    return Object.keys(miniBtnsMap).filter((key) => miniBtnsMap[key]);
  },

  // 底部展示的小按钮
  miniButtons(state, getters) {
    const {
      totalMiniButtons,
      plusBtnConfig: { showMoreBtn, maxMiniCount },
    } = getters;

    if (showMoreBtn) {
      const miniButtons = totalMiniButtons.slice(0, maxMiniCount - 1);
      miniButtons.push('more');
      return miniButtons;
    }

    return totalMiniButtons;
  },

  // 更多按钮浮起的小按钮
  plusButtons(state, getters) {
    const {
      totalMiniButtons,
      plusBtnConfig: { showMoreBtn, maxMiniCount },
    } = getters;

    return showMoreBtn ? totalMiniButtons.slice(maxMiniCount - 1) : [];
  },

  // 小按钮实际展示数据
  miniButtonsData(state, getters) {
    const { miniButtons, miniBtnsConfig } = getters;
    return miniButtons.map((key) => miniBtnsConfig[key]);
  },

  // 更多按钮实际展示数据
  plusButtonsData(state, getters) {
    const { plusButtons, miniBtnsConfig } = getters;
    return plusButtons.map((key) => miniBtnsConfig[key]);
  },

  // 限制购买的按钮
  disabledBigButton(state, getters) {
    const {
      // 是否外链
      isOutlink,
      // 是否上架
      isDisplay,
      // 仅限会员购买
      limitBuy,
      // 商品库存
      stockNum,
      // 限购引导信息
      buyLimitGuideInfo = {},
    } = state.goods;
    const { showForbidBuyBtn } = state.display;
    const { showStockLackReminder } = state.shopConfig;
    const { homepageUrl, isSupportFCodeBuy } = getters;
    const { skus } = state.staticSkuInfo;

    // 商品未售罄限流
    if (!state.fetchSuccess) {
      if (stockNum > 0) {
        return {
          forbid: true,
          text: '立即抢购',
          primary: true,
          skuScene: SKU_SCENE.FORBID,
        };
      }
      return {
        forbid: true,
        text: '已售罄，看看其他商品吧',
        skuScene: SKU_SCENE.FORBID,
      };
    }

    // F码购买
    if (isSupportFCodeBuy) {
      return {
        forbid: true,
        text: isSupportFCodeBuy ? 'F码购买' : '查看店铺其他商品',
        url: isSupportFCodeBuy
          ? '//h5.youzan.com/wscump/fcode/detail'
          : homepageUrl,
        primary: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }

    if (showForbidBuyBtn) {
      // 售罄补货提醒
      if (showStockLackReminder) {
        const remindBtnConfig = {
          text: state.hasRemindSpuStock ? '已提醒补货' : '提醒商家补货',
          primary: true,
          disabled: state.hasRemindSpuStock,
          skuScene:
            skus.length > 0 ? SKU_SCENE.NORMAL_BUY : SKU_SCENE.STOCK_REMINDER,
        };
        if (skus.length === 0 && !state.hasRemindSpuStock) {
          remindBtnConfig.subscribeSceneV2 = WX_SUBSCRIBE_SCENE.STOCK_REMINDER;
        }
        return [
          {
            forbid: true,
            text: '查看其他商品',
            url: homepageUrl,
            skuScene: SKU_SCENE.FORBID,
          },
          remindBtnConfig,
        ];
      }

      return {
        forbid: true,
        text: '查看店铺其他商品',
        url: homepageUrl,
        primary: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }

    // 商品下架
    if (isDisplay !== 1) {
      return {
        forbid: true,
        text: '下架啦，看看其他商品吧！',
        url: homepageUrl,
        primary: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }
    // 外链商品
    if (isOutlink) {
      return {
        forbid: true,
        text: '商品已下架，去看看其他商品吧',
        url: homepageUrl,
        primary: true,
        disabled: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }
    // 特定会员购买
    if (limitBuy) {
      // 需要引导
      if (buyLimitGuideInfo.needGuide) {
        return getters.buyLimitGuideButton;
      }
      return {
        forbid: true,
        text: '仅限特定会员购买',
        disabled: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }
    // 售罄
    if (stockNum <= 0) {
      return {
        forbid: true,
        text: '已售罄，看看其他商品吧',
        url: homepageUrl,
        primary: true,
        skuScene: SKU_SCENE.FORBID,
      };
    }
    return null;
  },

  // 普通购买的按钮
  normalBuyBigButton(state, getters) {
    const {
      display,
      goods,
      shopConfig,
      env = {},
      buttonUmpTextModel = {},
    } = state;
    const { accountUnionScene: ACCOUNT_UNION_SCENE = {} } =
      state.businessConfigs;
    const bigButtons = [];
    // 周期购且新希望店铺需要显示加入购物车
    if (display.showCartBtn || (getters.isPeriodbuy && env.isNewHopeKdt)) {
      bigButtons.push({
        text: `加入${getters.cartText}`,
        skuScene: SKU_SCENE.ADD_CART,
        accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
      });
    }
    // 积分商品可以与其他活动叠加
    if (display.showPointsBtn && getters.isPointsExchange) {
      bigButtons.push({
        text: `${goods.pointsName}兑换`,
        skuScene: SKU_SCENE.POINTS_BUY,
        disabled: getters.showPointsButtonStatus === 1, // 0:不展示（无积分活动、失效、未开始等），1：展示但置灰（有商品库存无积分库存，可以直接购买），2：展示（可正常兑换）
        accountUnionScene: ACCOUNT_UNION_SCENE.POINTS_BUY,
      });
    }
    if (
      (display.showBuyBtn && shopConfig.isShowBuyBtn) ||
      bigButtons.length === 0
    ) {
      let skuScene = SKU_SCENE.NORMAL_BUY;
      if (!display.showCartBtn) {
        skuScene = SKU_SCENE.SEL_SKU;
      }
      if (
        getters.discountActivity &&
        getters.discountActivity.type === 'goodsScan'
      ) {
        // 如果扫码优惠，则为营销活动购买
        skuScene = SKU_SCENE.ACT_BUY;
      }
      bigButtons.push({
        text: buttonUmpTextModel.buyButtonText || getters.bottomBuyBtnTxt,
        sub: buttonUmpTextModel.umpTag || getters.preferentialPrice || '',
        primary: true,
        skuScene,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      });
    }
    return bigButtons;
  },

  // 在disabled button基础上对限时折扣、定时开始做处理，因为可以叠加其他活动
  // 前处理逻辑
  commonBigButtons(state, getters) {
    const { waitToSold } = state.goodsActivity;
    const { showCartBtn } = state.display;
    const { disabledBigButton } = getters;

    if (disabledBigButton) {
      return Array.isArray(disabledBigButton)
        ? disabledBigButton
        : [disabledBigButton];
    }

    const bigButtons = [];
    const { accountUnionScene: ACCOUNT_UNION_SCENE = {} } =
      state.businessConfigs;

    // 定时开售，允许加入购物车
    if (waitToSold) {
      // 可加入购物车的定时开售商品显示加入购物车
      if (showCartBtn) {
        bigButtons.push({
          text: `加入${getters.cartText}`,
          skuScene: SKU_SCENE.ADD_CART,
          accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
        });
      }
      if (getters.isProductLaunch) {
        bigButtons.push(getters.productLaunchButtons);
      } else if (state.display.showSaleReminder) {
        bigButtons.push({
          primary: true,
          skuScene: SKU_SCENE.SALE_REMINDER,
          subscribeSceneV2: state.hasAddSaleReminder
            ? undefined
            : WX_SUBSCRIBE_SCENE.SALE_REMINDER,
          disabled: state.hasAddSaleReminder,
          text: state.hasAddSaleReminder ? '已设置提醒' : '开售提醒我',
        });
      }

      if (bigButtons.length === 0) {
        bigButtons.push({
          forbid: true,
          text: '即将开售',
          disabled: true,
        });
      }

      return [...bigButtons];
    }

    // 添加定金膨胀的逻辑
    if (getters.isHelpDepositPresale) {
      return [...getters.helpDepositPresaleButtons];
    }

    // 限时折扣按钮逻辑与普通商品相同
    // 前处理用于与积分商品等活动叠加
    if (getters.isTimelimitedDiscount) {
      return [...getters.normalBuyBigButton];
    }
    return [...bigButtons];
  },

  // 是否是限制购买，如果限制购买，则不需要活动按钮逻辑及预售逻辑
  isBigButtonForbidBuy(state, getters) {
    const btns = getters.commonBigButtons;
    return btns.length > 0 && btns[btns.length - 1].forbid;
  },

  // 大按钮
  /**
   * 这个函数可以在各个活动页面重写，普通商品页返回common
   * button config 参数
   * @param {boolean} forbid - 是否限制购买
   * @param {string} text - 按钮主文案
   * @param {string} sub - 按钮副文案
   * @param {string} leftText - 长按钮左侧主文案
   * @param {string} leftSub - 长按钮左侧副文案
   * @param {string} url - 跳转链接
   * @param {boolean} primary - 是否主按钮
   * @param {boolean} disabled - 是否不可点击
   * @param {boolean} share - 是否为分享
   * @param {string} skuScene - 规格使用场景
   * @param {string} accountUnionScene - 用户权限检测
   * @return {*}
   */
  bigButtons(state, getters) {
    const { commonBigButtons } = getters;
    if (commonBigButtons.length > 0) {
      return commonBigButtons;
    }
    // 这个函数可以在各个活动页面重写
    // 由bigButtonsData负责统一处理叠加活动逻辑
    return [];
  },

  // 后处理逻辑，待展示的按钮配置
  // 如果没有叠加其他活动，且商品可购买，初始bigButtons应该为空数组
  bigButtonsData(state, getters) {
    // 如果限制购买，则不展示额外逻辑
    if (getters.isBigButtonForbidBuy) {
      return getters.commonBigButtons;
    }

    let bigButtons = [...getters.bigButtons];
    // 如果是预售
    const { isPresale, isDepositPresale, depositShowPrice } = getters;
    const presaleDesc = getters.presaleDesc();
    const { buttonUmpTextModel = {} } = state;

    if (isPresale && presaleDesc) {
      let primaryBtn = null;
      if (bigButtons.length > 1) {
        return bigButtons;
      }
      if (bigButtons.length === 1) {
        primaryBtn = {
          ...bigButtons[0],
          leftText: presaleDesc.desc,
          leftSub: presaleDesc.subDesc,
        };
        if (primaryBtn.skuScene === SKU_SCENE.SEL_SKU) {
          primaryBtn.skuScene = SKU_SCENE.NORMAL_BUY;
        }
      } else {
        const { accountUnionScene: ACCOUNT_UNION_SCENE = {} } =
          state.businessConfigs;
        primaryBtn = {
          leftText: presaleDesc.desc,
          leftSub: presaleDesc.subDesc,
          text: isDepositPresale
            ? '支付定金'
            : buttonUmpTextModel.buyButtonText || getters.bottomBuyBtnTxt,
          primary: true,
          sub: isDepositPresale ? depositShowPrice : buttonUmpTextModel.umpTag,
          skuScene: SKU_SCENE.NORMAL_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
        };
        if (
          getters.discountActivity &&
          getters.discountActivity.type === 'goodsScan'
        ) {
          // 如果扫码优惠，则为营销活动购买
          primaryBtn.skuScene = SKU_SCENE.ACT_BUY;
        }
      }
      return [primaryBtn];
    }

    // 如果是空数组，需要复制普通购买默认按钮
    if (bigButtons.length === 0) {
      bigButtons = [...getters.normalBuyBigButton];
    }
    // 去校验按钮逻辑，如果只有一个按钮，则表现为主按钮
    if (bigButtons.length === 1) {
      bigButtons[0].primary = true;
    }
    return bigButtons;
  },

  parsedWebImInGoodsConfig(state) {
    const { webImInGoodsConfig } = state.shopConfig;

    return webImInGoodsConfig
      ? JSON.parse(webImInGoodsConfig)
      : {
          default: 0,
          label: '',
        };
  },

  bottomImBtnTxt(state, getters) {
    const {
      parsedWebImInGoodsConfig: { default: defaultConfig, label = '客服' },
    } = getters;

    return +defaultConfig === 1 ? label : '客服';
  },

  bottomBuyBtnTxt(state) {
    const {
      display: { showDefaultBuyButtonText, buyButtonText = '立即购买' },
    } = state;

    return showDefaultBuyButtonText ? '立即购买' : buyButtonText;
  },

  isShowInSourcingFissionTip() {
    return false;
  },

  // 是否售罄
  isSoldOut(state, getters) {
    // 商品未下架、非外链、未售罄
    return (
      state.goods.isDisplay === 1 &&
      !getters.showAvailableStores &&
      !state.goods.isOutlink &&
      !state.goods.stockNum &&
      !getters.isSupportFCodeBuy
    );
  },
  /**
   *
   * 是否可以直接到下单页（项目：无规格商品直接下单）10个字段判断
   *  判断sku组件页面除购买件数之外是否有需用户操作信息：规格列表，商品属性，周期购，阶梯团，价格日历，分期付款，先用后付，留言，卡券说明（后续可能会新增其他新模块）
   * @param {*} state
   * @param {*} getters
   */
  isDirectOrder(state, getters) {
    const { isPeriodbuy, isPriceCalendar, showVirtualTicketIntro } = getters;
    const {
      originSku = {},
      goods = {},
      payWays = {},
      goodsActivity = {},
      pageSwitch,
    } = state;
    const { noneSku, messages = [] } = originSku;
    const { itemSalePropList = [] } = goods;
    const { instalment, priorUse } = payWays;
    const { catering } = goodsActivity;
    // apollo 控制走老逻辑或者新逻辑；
    const { close_none_sku_goods_jump } = pageSwitch;
    const close = close_none_sku_goods_jump;
    return (
      noneSku &&
      !itemSalePropList.length &&
      !catering &&
      !isPeriodbuy &&
      // !isLadderGroupOn &&
      !isPriceCalendar &&
      !instalment &&
      !priorUse &&
      !messages.length &&
      !showVirtualTicketIntro &&
      !close
    );
  },

  // 限购引导按钮
  buyLimitGuideButton(state) {
    const { kdtId } = state.shop;
    const { buyLimitGuideInfo = {} } = state.goods;
    const { guideType, guideTargetAlias } = buyLimitGuideInfo;
    const currentPage = getCurrentPages();
    const { route, options } = currentPage[currentPage.length - 1];
    const redirectUrl = args.add(`/${route}`, options);
    let goUrl = '';
    let btnTxt = '';
    let ei = '';
    let en = '';
    switch (guideType) {
      // 免费会员
      case 1:
        goUrl = args.add(
          '/wscuser/levelcenter/fill',
          {
            kdt_id: kdtId,
            alias: guideTargetAlias,
            guideType: 'goods',
            redirectUrl,
            eT: Date.now(),
          },
          true
        );
        btnTxt = '仅限会员购买，一键注册';
        ei = 'view_button_free';
        en = '商详页_限购按钮曝光_免费会员';
        break;
      // 付费会员
      case 2:
        goUrl = args.add(
          'https://cashier.youzan.com/pay/wscuser_paylevel',
          {
            kdt_id: kdtId,
            alias: guideTargetAlias,
            guideType: 'goods',
            redirectUrl,
          },
          true
        );
        btnTxt = '仅限会员购买，点击办理';
        ei = 'view_button_pay';
        en = '商详页_限购按钮曝光_付费会员';
        break;
      // 免费权益卡
      case 3:
        goUrl = args.add(
          '/packages/card/detail/index',
          {
            kdt_id: kdtId,
            alias: guideTargetAlias,
            shopAutoEnter: 1,
            guideType: 'goods',
            redirectUrl,
          },
          true
        );
        btnTxt = '限持卡客户购买，一键领卡';
        ei = 'view_button_card';
        en = '商详页_限购按钮曝光_权益卡';
        break;
      // 付费权益卡
      case 4:
        goUrl = args.add(
          '/packages/card/detail/index',
          {
            kdt_id: kdtId,
            alias: guideTargetAlias,
            shopAutoEnter: 1,
            guideType: 'goods',
            redirectUrl,
          },
          true
        );
        btnTxt = '限持卡客户购买，立即开卡';
        ei = 'view_button_card';
        en = '商详页_限购按钮曝光_权益卡';
        break;
      default:
        break;
    }
    // 曝光打点
    setLogger({
      et: 'view',
      ei,
      en,
    });

    return {
      forbid: true,
      text: btnTxt,
      url: goUrl,
      primary: true,
      skuScene: SKU_SCENE.LIMIT_GUIDE,
      isLimitBuyGuide: true,
    };
  },
  isDisplayLimitedSeckillPop() {
    return false;
  },
};
