import { GoodsComponent } from '@goods/common/vanx-component';
import { mapActions } from '@youzan/vanx';

GoodsComponent({
  name: 'SubmitBlock',

  state: ['env'],

  methods: mapActions(['refreshCartCount', 'getThemeType']),

  ready() {
    this.refreshCartCount();
    this.getThemeType();
    this.on('refreshCartCount', this.refreshCartCount);
  },

  detached() {
    this.off('refreshCartCount', this.refreshCartCount);
  },
  pageLifetimes: {
    show() {
      this.refreshCartCount();
    },
  },
});
