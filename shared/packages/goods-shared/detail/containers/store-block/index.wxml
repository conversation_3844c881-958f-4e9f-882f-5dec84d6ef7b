<common-cell
  wx:if="{{ storeInfo }}"
  title="门店"
  border="{{ false }}"
  isLink="{{ true}}"
  value-class="store-cell__value"
  bind:click="goStoreList"
>
  <view class="store-cell__info">
    <view class="store-cell__main">
      <text class="store-cell__name">{{ storeInfo.physicalStoreName }}</text>
      <text wx:if="{{ offlineStore.storeCount > 1 }}" class="store-cell__num">共{{ offlineStore.storeCount }}家门店</text>
    </view>
    <view class="store-cell__address">
      <text wx:if="{{ storeInfo.distance }}" class="store-cell__distance">{{ storeInfo.distance }}</text>
      {{ storeInfo.physicalStoreAddress }}
    </view>
  </view>
</common-cell>
