@import '~shared/common/css/_variables.scss';

.store-cell {
  &__value {
    display: block;
  }

  &__info {
    display: flex;
    flex-direction: column;
  }

  &__num {
    margin-left: 4px;
    color: $gray-dark;
  }

  &__main {
    display: flex;
    justify-content: space-between;
  }

  &__name {
    flex: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    color: $text-color;
  }

  &__address {
    margin-top: 4px;
    line-height: 18px;
    text-align: left;
    color: $gray-dark;
  }

  &__distance {
    position: relative;
    padding-right: 8px;

    &::after {
      position: absolute;
      box-sizing: border-box;
      content: ' ';
      top: 3px;
      bottom: 1px;
      right: 5px;
      border-left: 1px solid $gray-dark;
      transform: scaleX(0.5);
    }
  }
}
