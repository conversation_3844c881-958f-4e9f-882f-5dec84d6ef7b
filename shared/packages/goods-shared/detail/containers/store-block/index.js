import { GoodsComponent } from '@goods/common/vanx-component';
import { mapState } from '@youzan/vanx';
import { get, formatDistance, jumpLink } from '@goods/utils';
import { setLogger } from '@goods/common/logger';

GoodsComponent({
  name: 'StoreBlock',

  state: ['offlineStore'],

  mapData: {
    ...mapState({
      storeInfo(state) {
        if (get(state, 'offlineStore.storeCount', 0) > 0) {
          setLogger({
            et: 'view',
            ei: 'view_physical_store',
            en: '门店信息曝光',
          });
          const { physicalStoreName, physicalStoreAddress, distance } =
            state.offlineStore.storeList[0] || {};
          const formatedDistance = distance
            ? formatDistance(+distance * 1000)
            : '';
          return {
            physicalStoreName,
            physicalStoreAddress,
            distance: formatedDistance,
          };
        }
        return null;
      },
    }),
  },

  methods: {
    goStoreList() {
      setLogger({
        et: 'click',
        ei: 'click_physical_store',
        en: '点击门店信息',
      });

      jumpLink('/packages/shop/physical-store/index');
    },
  },
});
