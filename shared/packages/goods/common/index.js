function isObject(value) {
  var type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}

// toFixed polyfill
function toFixed(number, d) {
  let s = `${number}`;
  if (!d) d = 0;
  if (s.indexOf('.') === -1) s += '.';
  s += new Array(d + 1).join('0');
  if (new RegExp(`^(-|\\+)?(\\d+(\\.\\d{0,${d + 1}})?)\\d*$`).test(s)) {
    let s = `0${RegExp.$2}`;

    const pm = RegExp.$1;

    let a = RegExp.$3.length;

    let b = true;
    if (a === d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1], 10) > 4) {
        for (let i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i], 10) + 1;
          if (a[i] === 10) {
            a[i] = 0;
            b = i !== 1;
          } else break;
        }
      }
      s = a.join('').replace(new RegExp(`(\\d+)(\\d{${d}})\\d$`), '$1.$2');
    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, '');
  }
  return `${number}`;
}

function formatDistance(distance) {
  if (!distance) {
    return '';
  }
  if (distance <= 100) {
    return '<100m ';
  }
  if (distance > 100 && distance <= 1000) {
    return `${toFixed(distance, 0)}m `;
  }
  if (distance > 1000 && distance < 10000) {
    return `${toFixed(distance / 1000, 1)}km `;
  }

  return '>10km ';
}

function numberToPrice(number, decimal = 2, leaveZero = false) {
  if (typeof number !== 'number') return '';
  const price = (number / 100).toFixed(decimal);
  if (leaveZero) {
    return parseFloat(price);
  }
  return price;
}

function formatPrice(price, separator = '~') {
  if (isObject(price)) {
    return numberToPrice(price.min) + separator + numberToPrice(price.max);
  }
  return numberToPrice(price);
}

function or(v1, v2) {
  if (typeof v1 === 'undefined') {
    return v2;
  }

  return v1;
}

export {
  formatDistance,
  numberToPrice,
  formatPrice,
  or,
};
