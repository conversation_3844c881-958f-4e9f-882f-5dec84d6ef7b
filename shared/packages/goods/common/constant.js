// 商品详情页列表
export const GOODS_TYPE_MAP = {
  COMMON: 'common', // 普通商品
  GROUP_ON: 'groupOn', // 拼团商品[ACT]
  SECKILL: 'seckill', // 秒杀
  LUCKY_DRAW_GROUP: 'luckyDrawGroup', // 抽奖拼团
  HELP_CUT: 'helpCut', // 砍价
  TUAN: 'tuan', // 团购
  BALE: 'bale', // 打包一口价
  TIME_LIMITED_DISCOUNT: 'timelimitedDiscount', // 限时折扣商品[ACT]
  MEED_REDUCE: 'meetReduce', // 满减送
  SUPPLIER_MEET_REDUCE: 'supplierMeetReduce', // 供货商满减送
  CASH_BACK: 'cashBack', // 满减送
  PACKET_BUY: 'packageBuy', // 优惠套餐
  PLUS_BUY: 'plusBuy', // 加价购
  PRE_SALE: 'presale', // 预售
  DEPOSIT_PRESALE: 'depositPresale', // 定金预售[ACT]
  // ??
  ENTITY_PRESALE: 'entityPresale', // 实物定金预售
  PERIOD_BUY: 'periodBuy', // 周期购
  VIRTUAL_TICKET: 'virtualTicket', // 电子卡券
  POINTS_EXCHANGE: 'pointsExchange', // 积分商品[ACT]
  GIFT: 'gift', // 送礼商品
  PRESENT: 'present', // 赠品
  // ??
  WAIT_TO_SOLD: 'waitToSold', // 定时售
  MEMBER_DISCOUNT: 'memberDiscount', // 会员折扣商品[ACT]
  LIMIT_BUY: 'limitBuy', // 仅限会员购买的商品
  LINK: 'link', // 外部链接
};

// 用于account-union组件 scene校验
export const ACCOUNT_UNION_SCENE = {
  // 2 点击加入购物车 add_shopping_car
  ADD_CART: 'add_shopping_car',
  // 3 点击立即购买 click_buy_now
  NORMAL_BUY: 'click_buy_now',
  // 4 点击拼团购买 together_group_purchase
  GROUP_BUY: 'together_group_purchase',
  // 5 买家领取优惠券 get_coupon
  GET_COUPON: 'get_coupon',
  // 6 买家领取优惠码 get_discount_code
  GET_DISCOUNT: 'get_discount_code',
  // 7 买家点击签到 click_sign_in
  SIGN_IN: 'click_sign_in',
  // 8 买家进行积分兑换 points_exchange
  POINTS_BUY: 'points_exchange',
};

// 按钮的场景信息，根据场景弹出相应的SKU或其他操作
export const SKU_SCENE = {
  // 普通购买
  NORMAL_BUY: 'normalBuy',
  // 活动购买，如团购、秒杀、拼团。。。
  ACT_BUY: 'actBuy',
  // 积分
  POINTS_BUY: 'pointsBuy',
  // 赠品
  PRESENT_BUY: 'presentBuy',
  // 送礼
  GIFT_BUY: 'giftBuy',
  // 加入购物车
  ADD_CART: 'addCart',
  // 选择SKU-会显示 加入购物车/购买
  SEL_SKU: 'selSku',
  // 空按钮，无法点击
  EMPTY: 'empty',
  // 分享
  SHARE: 'share',
  // 预约 如秒杀预约
  RESERVATION: 'reservation',
  // 开始砍价
  START_CUT: 'startCut',
  // SAAS
  SAAS: 'saas',
};

// sku 弹层处理参数 对应原来的displaySkuMap
export const SKU_CONFIG_MAP = {
  reset: {
    isMultiBtn: false,
    isAddCart: false,
    isPoints: false,
    type: '',
  },
  // 同时显示购物车与购买按钮
  selectSku: {
    isMultiBtn: true,
    type: 'selectSku'
  },
  addCart: {
    isAddCart: true,
    isMultiBtn: false,
    type: 'addCart'
  },
  buy: {
    isAddCart: false,
    isMultiBtn: false,
    type: 'buy'
  },
  gift: {
    isMultiBtn: false,
    isAddCart: false,
    isGift: true,
    type: 'gift'
  },
  point: {
    isAddCart: false,
    isMultiBtn: false,
    isPoints: true,
    type: 'point'
  },
  presale: {
    isAddCart: false,
    isMultiBtn: false,
    type: 'presale'
  },
  hotel: {
    isAddCart: false,
    isMultiBtn: false,
    type: 'buy'
  },
  addWish: {
    isAddWish: true,
    isAddCart: false,
    isMultiBtn: false,
    type: 'addWish'
  }
};

// 认证信息
export const TEAM_CERT = {
  2: {
    type: 'team_certificate_company',
    tag: '企业认证'
  },
  3: {
    type: 'team_certificate_person',
    tag: '个人认证'
  },
  // 快速个人认证
  4: {
    type: 'team_certificate_person',
    tag: '个人认证'
  },
  // 旗舰店
  6: {
    type: 'team_certificate_qijian',
    tag: '官字店认证'
  },
  // 专卖店
  7: {
    type: 'team_certificate_zhuanmai',
    tag: '官字店认证'
  },
  // 直营店
  8: {
    type: 'team_certificate_zhiying',
    tag: '官字店认证'
  },
  // 专营店
  9: {
    type: 'team_certificate_zhuanying',
    tag: '官字店认证'
  }
};

// 活动类型 用于日志
export const ACTVITY_TYPE_ID_MAP = {
  luckyDrawGroup: '23',
  helpCut: '21',
  groupOn: '4',
  tuan: '2',
  seckill: '6',
  timelimitedDiscount: '11',
  pointsExchange: '5',
  discount: '10',
  depositExpansion: '114',
  meetReduce: '101', // 满减
  cashBack: '102', // 订单返现
  bale: '104', // 打包一口价
  present: '8'
};
