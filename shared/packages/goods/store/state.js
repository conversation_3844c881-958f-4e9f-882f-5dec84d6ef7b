const state = {
  url: {},
  // 环境配置
  env: {
    // platform,
    // isWeixin: ua.isWeixin(),
    // isIOS: ua.isIOS(),
    // isAndroid: ua.isAndroid(),
  },

  // 页面基本配置 传参等，不会被重置
  pageParams: {
    alias: '',
    uniqueAlias: '', // 小程序该商品唯一标识
    path: '',
    activityType: '',
    activityId: '',
    sls: '', // 分销员alias
    present: '' // 赠品标识
  },

  shareParams: {},

  // 主图信息
  picture: {
    // 商品图片列表
    pictures: [],
    // 服务端评估图片高度
    height: 320
  },

  // 视频
  video: {
    width: 0,
    height: 0,
    coverUrl: '',
    videoUrl: '',
    // 统计播放
    countPlayedUrl: ''
  },

  // 商品基本信息
  goods: {
    id: 0,
    alias: '',
    title: '',
    // 商品页面路径
    path: '',
    // 商品头图，用于sku展示
    picture: '',
    // 商品卖点，页面子标题内容
    sellPoint: '',
    // 划线价，是个字符串
    origin: '',
    // 是否是虚拟商品，包含 虚拟商品和电子卡券
    isVirtual: 0,
    // 是否是电子卡券
    isVirtualTicket: 0,
    // 是否分期
    isInstallment: false,
    // 是否上架
    isDisplay: 0,
    // 是否仅限特定会员购买 || 限定积分购买
    limitBuy: 0,
    // 定时开售时间
    waitToSoldTime: 0,
    // 购买方式 0：外购买商品 1：非外部购买商品
    buyWay: 1,
    // 外链商品购买链接
    buyUrl: '',
    // 不可购买原因
    forbidBuyReason: '',
    // 商品是否参与f码活动
    isSupportFCode: false,
    // 商品是否被收藏
    isGoodsCollected: false,
    // 商品属性
    itemSalePropList: []
  },

  supplier: {
    // 分销商对应的供货商kdtId
    kdtId: '',
    // 推广模式
    mode: ''
  },

  // 分销员
  salesman: {
    // 分销员标识 seller
    alias: '',
    goodsPrice: '',
    // 短链接
    shareLink: ''
  },

  // 退款模型
  refund: {
    // 是否支持退款（包含虚拟商品和电子卡券）
    isSupport: 1,
    // 退款方式
    type: '',
    // 退款时间区间
    interval: ''
  },

  // 限购信息
  limit: {
    // 商品限购数
    num: 0,
    // 已购买数
    used: 0,
    // 限购周期
    period: 0,
    // 起售数量
    startSaleNum: 1
  },

  // 店铺信息
  shop: {
    kdtId: 0,
    logo: '',
    name: '',
    // 客服电话
    phone: '',
    url: '',
    // 【组织认证groupCertType】
    // 1 网店;
    // 256789 企业（5-9是历史数据）;
    // 3 个人;
    // 4 个人快捷;
    // 10 组织（老的认证里代表公益组织）;
    // 12 个体工商户;
    // 【店铺认证shopCertType】
    // 6  旗舰店;
    // 7 专卖店;
    // 8 直营店;
    // 9 特许经营店;
    // 11  普通店铺;
    certType: 0,
    // 7表示零售店铺
    shopType: 0
  },

  // 购物车信息
  cartInfo: {
    count: 0
  },

  // 多网点信息
  multistore: {
    id: 0,
    name: '',
    address: '',
    distance: 0,
    // 其他可售门店
    salableStores: [],
    // 是否开启网点切换
    openMultiStoreSwitch: '',
    hideStore: false
  },

  // 店铺配置
  shopConfig: {
    // 商品页是否展示立即购买按钮
    showBuyBtn: false,
    // 是否加入担保交易
    isSecuredTransactions: false,
    // 是否开启推荐商品
    showRecommendGoods: false,
    // 是否开启销量与成交记录
    showBuyRecord: false,
    // 是否开启商品评价
    showCustomerReviews: false,
    // 是否开启买家秀
    showBuyerShows: false,
    // 是否支持运费险
    // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
    supportFreightInsurance: false,
    // 是否隐藏购物车按钮
    hideShoppingCart: false,
    // 是否有线下门店
    hasPhysicalStore: false
  },

  // 店铺担保配置
  guarantee: {
    // 是否加入有赞担保
    on: false,
    // 担保样式
    style: 0
  },

  // 零售店铺信息
  retail: {
    // 是否展示零售门店标签
    show: false,
    // 零售门店地址
    address: ''
  },

  // 配送信息
  distribution: {
    // 运费
    postage: '',
    // 是否支持快递
    supportExpress: false,
    // 是否支持自提
    supportSelfFetch: false,
    // 是否支持同城送
    supportLocalDelivery: false,
    // 快递费用
    expressFee: '',
    // 同城送费用
    localDeliveryFee: ''
  },

  // 商品tab栏数据
  goodsDetail: {
    // 默认激活的详情tab
    defaultType: 'goods',
    // 模板风格 0：普通版 1：极速版
    // 小程序目前不支持极速版
    tplStyle: 0,
    // 富文本内容
    richText: '',
    // 商品详情组件数据
    showcaseComponents: [],
    themeFeature: {
      uniqueKey: '',
      componentsLength: ''
    }
  },

  // 成交记录
  tradeRecords: {
    list: [],
    page: 1,
    loading: false,
    finished: false
  },

  // 成交记录跑马灯
  tradeRecordsCarousel: [],

  // sku组件相关数据，不依赖ajax数据
  skuConfig: {
    // sku弹层触发类型 SKU_SCENE 中枚举的类型
    // 协同SKU_DISPLAY_MAP使用
    skuScene: '',
    // 用于account-union校验
    accountUnionScene: '',
    isMultiBtn: false,
    isAddCart: false,
    isGift: false,
    isPoints: false,
    // 由sku组件处理
    selectedSku: null,
    installmentRate: 0, // 费率
    selectedInstallmentPeriod: 0 // 分期数
  },

  // 有些商品活动可能会覆盖sku-extra
  skuExtraData: {
    useCustomHeaderPrice: true,
    priceData: {
      showPrice: '0.00',
      showOldPrice: null,
      showPointsPrice: ''
    },
    activityInfo: {},
    selectedSku: null,
    selectedProp: null,
    selectedSkuComb: null,
    selectedSkuDate: null,
    selectedScale: null
  },

  // sku 详细信息，后端传入
  sku: {},
  originSku: {},

  // 推荐商品列表
  recommendGoods: {
    list: [],
    page: 1,
    loading: false,
    finished: false
  },

  // 当前商品页活动标存储一份，方便拼团或者积分兑换下单传参用
  activityInfo: {
    // 活动alias
    activityAlias: '',
    // 活动id
    activityId: 0,
    // 活动类型
    activityType: 0
  },

  // 页面展示层控制；Ajax获得的数据
  display: {},

  // 浮层处理，不依赖Ajax display
  displayPop: {
    // 是否显示分享
    sharePopShow: false,
    skuPopShow: false
  },

  // 价格日历数据
  priceCalendarData: {
    nearlyFourMonthsPriceRange: {},
    nearlyFourMonthMinPriceMap: {},
    nearlyFourDayMarketableMap: {},
    ecardPriceCalendarModelMap: {},
    // virtual
    skuId: 0,
    monthArr: [],
    // skubar价格日历列表
    priceBarList: [],
    priceDateRange: '',
    submitPriceDate: []
    // virtual end
  },

  // 各种活动，可能不存在，所以先观察

  // 商品级别营销活动，需要带 activityId/alias 到下单流程，才能触发活动
  marketActivity: {
    // 0元拼团
    luckyDrawGroup: null,
    // 砍价0元购
    helpCut: null,
    // 拼团商品
    groupOn: null,
    // 秒杀商品
    seckill: null,
    // 积分商品
    points: null,
    // 赠品，通过幸运大抽奖等方式获取
    present: null,
    // 团购
    tuan: null
  },

  // vanx必须要有初始值
  orderPromotion: {},

  // 订单级别活动
  // 不需要携带 alias，在交易流程会自动触发折扣/返现
  orderActivity: {
    // 打包一口价
    bale: null,
    // 订单返现
    cashBack: null,
    // 满减送营销活动
    meetReduce: null,
    // 供货商满减送
    supplierMeetReduce: null,
    // 包邮工具
    carriageDiscount: null,
    // 优惠套餐
    packageBuy: {},
    // 加价购
    plusBuy: null,
    // 扫码会员或者会员折扣
    discount: null,
    // 限时折扣
    timelimitedDiscount: null,
    // 引导用户进行开会员卡, 小程序上暂时不支持
    discountInvite: null,
    // 定金膨胀，需要配套预售使用
    depositExpansion: null
  },

  // 商品自身属性带来的折扣活动
  goodsActivity: {
    // 预售信息
    presale: null,
    // 周期购 活动信息 periodBuyExtra
    periodbuy: null,
    // 电子卡券
    virtualTicket: null,
    // 送礼商品
    gift: null,
    // 仅限会员购买
    limitBuy: null
  },

  // 海淘
  haitao: {
    isHaitao: false,
    haiTaoItemExtra: null,
    haiTaoTradeMode: 0
  },

  // 用于让配置动态化，处理 +new Date() 之类的问题
  v: 0
};

export default state;
