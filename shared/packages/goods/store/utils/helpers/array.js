function minBy(array = [], iteratee) {
  if (!array || !array.length) {
    return;
  }

  return baseExtremum(array, iteratee, (a, b) => a < b);
}

function maxBy(array = [], iteratee) {
  if (!array || !array.length) {
    return;
  }

  return baseExtremum(array, iteratee, (a, b) => a > b);
}

function baseExtremum(array, iteratee, comparator) {
  let computed;
  let result;
  array.forEach((item) => {
    const current = iteratee(item);
    const isExistCurrent = current !== null && !isNaN(current);
    if (!isExistCurrent) {
      return;
    }

    if (computed === undefined || comparator(current, computed)) {
      computed = current;
      result = item;
    }
  });

  return result;
}

export { minBy, maxBy };
