import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import { moment } from '@youzan/weapp-utils/lib/time';
import { node as request } from 'shared/utils/request';
import adaptorComponents from 'constants/adaptor-components';
import { showWarningModal, doRedirectPage } from './helpers';

const app = getApp();

// 获取商品详情
export function getGoodsDetail(params = {}, options = {}) {
  // 如果没有alias，则直接返回
  if (!params.alias) {
    return Promise.reject('无 alias');
  }
  const path = '/wscshop/goods-api/detail.json';
  const query = {
    alias: params.alias,
    activityId: params.activityId,
    activityType: params.activityType,
    type: params.activityType, // ?? 接口兼容， 0元购使用activityType，helpCut使用type
    pageType: params.pageType,
    checkAlias: true,
  };
  if (params.present) {
    // 赠品标识
    query.present = params.present;
  }
  const { redirectCount } = params; // 限流页面的页面跳转次数
  return new Promise((resolve, reject) => {
    request({
      path,
      query,
      config: {
        cache: true,
        expire: 3,
        skipShopInfo: options.skipShopInfo,
      },
      success: (res = {}) => {
        const { goods = {} } = res;
        const { goods: goodsData = {} } = goods;
        const isOutGoods = goodsData.buyWay === 0;
        if (isOutGoods) {
          showWarningModal({
            content: '小程序暂不支持外链商品',
          });
          return reject({ code: -1, msg: '外链商品不支持展示' });
        }

        if (goodsData.kdtId) {
          app.trigger('update:youzan:kdtId', goodsData.kdtId, {
            mark: '917',
          });
        }

        resolve(res);
      },
      fail: ({ code, msg, res: allResData = {} }) => {
        // const { res: allResData } = res;
        // 连锁进店外部处理
        if (code === 'chainstore need enter shop') {
          reject({ code, msg });
          return;
        }
        //  多网点
        if (code === 160900100) {
          reject({ code: -1, msg: '多网点，需要跳转至网点选择' });
          return wx.navigateTo({
            url: '/packages/shop/multi-store/index/index',
          });
        }
        if (code === 429 || code === 101302001 || code === 101302002) {
          reject({ code: -1, msg: '店铺太火爆啦，请稍后重试' });
          const pages = getCurrentPages() || [];
          const containerPageRoute = get(pages[pages.length - 1], 'route');
          const containerPageOption = get(pages[pages.length - 1], 'options');
          return wx.redirectTo({
            url: args.add('/packages/common/limit-page/index', {
              redirectCount,
              callBackUrl: `/${containerPageRoute}`,
              options: JSON.stringify(containerPageOption),
            }),
          });
        }
        // 知识付费或其他重定向
        if (code === 100302) {
          const { data = {} } = allResData;
          const { metadata: redirectData = {} } = data;
          doRedirectPage(redirectData, params);
          return;
        }
        showWarningModal({
          content: msg || '网络开了个小差，请稍后再试',
        });
        reject({ code: -1, msg });
      },
    });
  });
}

export function getShowcaseComponents(alias = '') {
  return request({
    path: '/wscshop/goods/showcase-components.json',
    query: {
      alias,
      adaptorComponents: adaptorComponents.join(','),
    },
    config: {
      cache: true,
      expire: 10,
    },
  });
}

/**
 * 获取SKU价格日历--电子卡券/虚拟商品
 * @param {String} params.activityType
 * @param {String} params.activityAlias
 * @param {String} params.itemId
 * @param {String} params.itemSkuId
 * @param {String} params.itemPreOrderTime 可预订时间
 */
export function getGoodsListDate(params = {}) {
  // 取四个月时间
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + 4);
  endDate.setDate(0);

  return new Promise((resolve, reject) => {
    request({
      path: '/wscshop/goods/list-date.json',
      query: {
        ...params,
        kdtId: app.getKdtId(),
        beginDate: moment(new Date(), 'YYYY-MM-DD'),
        endDate: moment(endDate, 'YYYY-MM-DD'),
      },
      config: {
        cache: true,
        expire: 300,
      },
      success: (res) => {
        console.log('fetch goods list-date success');
        resolve(res);
      },
      fail: (res) => {
        showWarningModal({
          content: res.msg,
        });
        reject({ code: -1, msg: res.msg });
      },
    });
  });
}

/**
 * 获取购物车商品数量
 */
export function getCartCount() {
  return request({
    path: '/wscshop/goods/cart-count.json',
    config: {
      cache: true,
      expire: 0,
    },
  });
}

export function getInstallmentPrice(query) {
  return request({
    path: '/wscshop/sku/installment-price.json',
    query,
  });
}

export function calculatePrice(data) {
  return request({
    path: '/wscgoods/detail-api/calculate-price.json',
    method: 'POST',
    data,
  });
}
