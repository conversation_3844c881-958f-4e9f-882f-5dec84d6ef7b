import get from '@youzan/weapp-utils/lib/get';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';
import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { COMMENT } from 'shared/common/sensetive-words';

function formatGoodsData(state = {}, payload = {}) {
  let parsedGoods = {
    pageParams: state.pageParams,
  };
  parsedGoods = initGoodsBaseData(parsedGoods, payload);
  parsedGoods = initGoodsData(parsedGoods, payload);

  return parsedGoods;
}

function makeTuanMonkey(arr) {
  return (arr || []).map((item) => {
    item.money = (item.value / 100.0).toFixed(2);
    return item;
  });
}

// 部分情况不支持商品属性，需要把相关配置删除
function unsupportItemSaleProp(state) {
  const { itemSalePropList = [] } = state.goods;
  if (itemSalePropList.length > 0) {
    state.goods.itemSalePropList = [];
  }
}

function initGoodsBaseData(parsedGoods, payload = {}) {
  const {
    // 所有商品相关的数据，中台的数据都在这里
    goods: goodsData,
    // 商品类型
    type = '',
    isHaitao = false,
    activityInfo = {},
  } = payload;

  const {
    // 商品
    goods = {},
    // 店铺
    shop = {},
    // 配送
    delivery = {},
    // 多网点
    multistore = {},
    // 页面渲染
    template = {},
    // 原始sku信息
    sku = {},
  } = goodsData;

  // 视频
  const videoConfig = get(goods, 'video', {});
  // 店铺配置
  const shopConfig = get(shop, 'config', {});
  // 退款设置
  const refund = get(goods, 'refundModel', {});
  // 海淘详细信息 !! 后端字段错别字
  const haiTaoItemExtra = get(goods, 'haoTaoItemExtra', null);
  const haiTaoTradeMode = get(haiTaoItemExtra, 'haiTaoTradeMode', 0);

  const { pageParams = {} } = parsedGoods;

  parsedGoods = {
    ...parsedGoods,
    // 主图信息
    picture: {
      pictures: goods.pictures || [],
      // 服务端评估图片高度
      height: goods.pictureHeight || 320,
    },

    // 视频
    video: {
      width: +videoConfig.coverWidth,
      height: +videoConfig.coverHeight,
      coverUrl: cdnImage(videoConfig.coverUrl || '', '!730x0.jpg'),
      videoUrl: videoConfig.videoUrl || '',
      // 统计播放
      countPlayedUrl: videoConfig.countPlayedUrl || '',
    },

    // 商品基本信息
    goods: {
      id: goods.id,
      // 商品类型
      type,
      alias: goods.alias || pageParams.alias,
      title: goods.title,
      // 小程序路径
      path: goods.path || pageParams.path,
      // 商品头图，用于sku展示
      picture: '',
      // 商品卖点，页面子标题内容
      sellPoint: goods.sellPoint || '',
      // 划线价
      origin: goods.origin,
      // 是否是虚拟商品，包含 虚拟商品和电子卡券
      isVirtual: !!goods.virtual,
      // 是否是电子卡券
      isVirtualTicket: type === 'ecard',
      // 是否分期
      isInstallment: goods.isInstallment,
      // 是否上架
      isDisplay: goods.isDisplay,
      // 是否仅限特定会员购买
      limitBuy: goods.purchaseLimit,
      // 定时开售时间
      waitToSoldTime: goods.startSoldRemainTime || 0,
      // 购买方式 0：外购买商品 1：非外部购买商品
      buyWay: goods.buyWay,
      // 外链商品购买链接
      buyUrl: goods.buyUrl,
      // 不可购买原因
      forbidBuyReason: template.forbidBuyReason,
      // 商品是否参与f码活动
      isSupportFCode: get(template, 'extra.supportFCode', false),
      // 商品是否被收藏，精选逻辑，小程序不需要支持
      // isGoodsCollected,
      // 店铺风险提示
      risk: goods.risk,
      // 商品属性
      itemSalePropList: goods.itemSalePropList || [],
    },

    haitao: {
      isHaitao,
      haiTaoItemExtra,
      haiTaoTradeMode,
    },

    supplier: {
      // 分销商对应的供货商kdtId
      kdtId: goods.supplierKdtId || '',
      // 推广模式
      mode: goods.fxTradeMode,
    },

    // 退款模型
    refund: {
      // 是否支持退款（包含虚拟商品和电子卡券）
      isSupport: get(refund, 'isSupportRefund', 1),
      // 退款方式
      type: refund.refundType,
      // 退款时间区间
      interval: refund.periodMillSeconds,
    },

    // 限购信息
    limit: {
      // 商品限购数
      num: goods.quota,
      // 已购买数
      used: goods.quotaUsed,
      // 限购周期
      period: goods.quotaCycle,
      // 起售数量
      startSaleNum: goods.startSaleNum || 1,
    },

    // 店铺信息
    shop: {
      kdtId: shop.kdtId,
      logo: shop.logo,
      name: shop.shopName,
      // 客服电话
      phone: shop.phone,
      // 店铺认证类型 2：企业认证 3-4：个人认证 5-9：官方认证
      certType: shop.shopCertType,
      // 7表示零售店铺
      shopType: shop.shopType,
    },

    // 多网点信息
    multistore: {
      id: multistore.storeId,
      name: multistore.name,
      address: get(multistore, 'address.address', ''),
      distance: multistore.distance,
      // 其他可售门店
      salableStores: multistore.salableStores || [],
      // 是否开启网点切换
      openMultiStoreSwitch: get(multistore, 'setting.openMultiStoreSwitch', ''),
      hideStore: get(multistore, 'setting.openHideStore', false),
    },

    // 店铺配置
    shopConfig: {
      // 商品页是否展示立即购买按钮
      showBuyBtn: !!shopConfig.showBuyBtn,
      // 是否加入担保交易
      isSecuredTransactions: shopConfig.isSecuredTransactions,
      // 是否开启推荐商品
      showRecommendGoods: shopConfig.goodsRecommend,
      // 是否开启销量与成交记录
      showBuyRecord: shopConfig.buyRecord,
      // 是否开启商品评价
      showCustomerReviews: shopConfig.customerReviews,
      // 是否开启买家秀
      showBuyerShows: shopConfig.buyerShow,
      // 是否支持运费险
      // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
      supportFreightInsurance: shopConfig.freightInsurance,
      // 是否隐藏购物车按钮
      hideShoppingCart: shopConfig.hideShoppingCart,
      // 是否有线下门店
      hasPhysicalStore: shopConfig.teamPhysical,
      // 微信自有支付
      isWeixinPayOrigin: +shopConfig.wxAppletOrigin === 1,
      // 成交记录设置 列表0， 悬浮窗1
      goodsDetailBuyRecord: shopConfig.goodsDetailBuyRecord || '',
      // 销售数量设置 // { show: 0 // 商详页销量是否展示：0-不展示，1-展示 limit: false,// 销量展示时是否限定值 limit_num:'',// 销量展示的限定值，大于0 }
      goodsDetailSales: shopConfig.goodsDetailSales || null,
    },

    // 店铺担保配置
    guarantee: {
      // 是否加入有赞担保
      on: shopConfig.isYouzanSecured,
      // 担保样式
      style: shopConfig.guaranteeShowStyleType,
    },

    // 零售店铺信息
    retail: {
      // 是否展示零售门店标签
      show: false,
      // 零售门店地址
      address: '',
    },

    // 配送信息
    distribution: {
      // 运费
      postage: get(delivery, 'postage.desc', ''),
      // 是否支持快递
      supportExpress: delivery.supportExpress,
      // 是否支持自提
      supportSelfFetch: delivery.supportSelfFetch,
      // 是否支持同城送
      supportLocalDelivery: delivery.supportLocalDelivery,
      // 快递费用
      expressFee: get(delivery, 'express.desc', ''),
      // 同城送费用
      localDeliveryFee: get(delivery, 'localDelivery.desc', ''),
    },

    // 当前商品页活动标存储一份，方便拼团或者积分兑换下单传参用
    // 实际marketActivity有效
    activityInfo: {
      // 活动alias
      activityAlias: activityInfo.alias || '',
      // 活动id
      activityId: activityInfo.id || 0,
      // 活动类型
      activityType: activityInfo.code || 0,
      // 活动名称
      activityName: activityInfo.name || '',
      // 活动类型名
      activityTypeName: activityInfo.type || '',
    },

    // 各种活动，可能不存在，所以先观察
    // 商品级别营销活动，需要带 activityId/alias 到下单流程，才能触发活动
    marketActivity: {
      // initGoodsData中添加
    },

    // 订单级别活动
    // 不需要携带 alias，在交易流程会自动触发折扣/返现
    orderActivity: {
      // initGoodsData中添加
    },

    // 商品活动
    goodsActivity: {
      // initGoodsData中添加
    },

    // 商品tab栏数据
    goodsDetail: {
      // 默认激活的详情tab，小程序目前没有这个需求
      defaultType: 'goods',
      // 模板风格 0：普通版 1：极速版
      tplStyle: template.itemDetailTplStyle,
      // 富文本内容
      richText: goods.content,
      // 商品详情组件数据
      showcaseComponents: [{}],
      themeFeature: {
        uniqueKey: '',
        componentsLength: 0,
      },
    },

    // 成交记录
    tradeRecords: {
      list: [],
      page: 1,
      loading: false,
      finished: false,
    },

    // 推荐商品列表
    recommendGoods: {
      list: [],
      page: 1,
      loading: false,
      finished: false,
    },

    // 弹层、按钮、类目等展示控制
    display: {
      // 是否展示小按钮列表，超过一定数量底部左侧小按钮会收起用更多替代
      showPlusButtons: false,
      // 是否展示订单返现弹层
      showCashBackPopup: false,
      // 是否展示团购返现弹层
      showTuanPopup: false,
      // 是否展示登录框
      showLoginPopup: false,
      // 是否展示日历
      showDatePicker: false,
      // 是否展示购买按钮
      showBuyBtn: template.showBuyButton,
      // 是否展示加入购物车按钮
      showCartBtn: template.showCartButton,
      // 是否展示积分兑换按钮
      showPointsBtn: template.showPointsButton,
      // 是否展示不可购买按钮，包含 下架、售罄等
      showForbidBuyBtn: template.showForbidBuyButton,
      // 是否展示送礼按钮
      showGiftBtn: template.showGiftButton,
      // 是否展示店铺按钮
      showMiniShopBtn: template.showMiniShopButton,
      // 是否展示小购物车按钮
      showMiniCartBtn: template.showMiniCartButton,
      // 是否展示小积分兑换按钮
      showMiniPointsBtn: template.showMiniPointsButton,
      // 是否展示分享按钮
      showShareBtn: template.showShareButton,
      // 是否展示im按钮
      showImBtn: template.showImButton,
      // 是否展示物流栏
      showDeliveryBar: template.showDeliveryBar,
      // 立即购买按钮文案 设置
      buyButtonText: template.buyButtonText,
      // 客服按钮文案 设置
      imButtonText: template.imButtonText,
      // 是否使用默认底部按钮
      showDefaultBuyButtonText: template.showDefaultBuyButtonText,
      // 是否展示默认客服文案
      showDefaultImButtonText: template.showDefaultImButtonText,
    },

    // 分销员
    salesman: {
      // 分销员标识 seller
      alias: '',
      goodsPrice: '',
      // 短链接
      shareLink: '',
    },

    originSku: sku,
  };

  return parsedGoods;
}

function initGoodsData(parsedGoods, payload = {}) {
  const { assign } = Object;
  const {
    // 所有商品相关的数据，中台的数据都在这里
    goods: goodsData,
    // 根据商品级别的活动类型，重新整理了数据格式，方便遍历
    parsedActivities,
    // 活动标志数据，已经在 Node 格式化，无需本地计算
    promotionTag,
    // 积分类型
    pointsData = '金币',
  } = payload;
  const { goods = {}, sku = {} } = goodsData;
  let {
    // 0元拼团
    luckyDrawGroup,
    // 砍价0元购
    helpCut,
    // 拼团
    groupOn,
    // 阶梯团
    ladderGroupOn,
    // 秒杀
    seckill,
    // 团购
    tuan,
    // 积分兑换
    pointsExchange,
    // 赠品
    presentExchange,
    // 限时折扣
    timelimitedDiscount,
    // 返现
    cashBack,
    // 满减
    meetReduce,
    // 供货商满减送
    supplierMeetReduce,
    // 包邮
    carriageDiscount,
    // 打包一口价
    bale,
    // 组合优惠
    packageBuy,
    // 加价购
    plusBuy,
    // 定金膨胀-跟预购挂钩
    depositExpansion,
    // 扫码优惠或者会员折扣
    discount,
    // 引导用户升级会员折扣
    discountInvite,
    // 助力定金膨胀
    helpDepositExpansion,
  } = parsedActivities || {};

  // 周期购
  const periodbuy = get(goods, 'periodBuyExtra', '');
  // 预售
  const presale = get(goods, 'preSaleInfo', '');
  // 电子卡券
  const virtualTicket = get(goods, 'ecardExtra', '');

  // 商品默认头图，sku弹层展示用
  const picture = cdnImage(get(parsedGoods.picture.pictures[0], 'url', ''));
  // 评论列表数据
  const list = {};
  const reviewTextMap = {
    0: COMMENT,
    10: '差评',
    20: '中评',
    30: '好评',
  };
  [0, 10, 20, 30].forEach((key) => {
    const reviewText = reviewTextMap[key];
    list[key] = {
      list: [],
      page: 1,
      total: 0,
      loading: false,
      finished: false,
      emptyText: `暂无${reviewText}`,
      reviewText,
    };
  });

  const {
    // 营销活动
    marketActivity,
    // 订单活动
    orderActivity,
    // 商品活动
    goodsActivity,
  } = parsedGoods;

  // sku头图
  assign(parsedGoods.goods, {
    picture,
  });

  // 格式化sku字段，组件需要，头图必须是驼峰
  assign(parsedGoods, {
    sku: formatSkuTree(sku),
  });

  // 商品详情
  parsedGoods.goodsDetail.showcaseComponents = [
    {
      type: 'rich_text',
      content: parsedGoods.goodsDetail.richText,
    },
  ];

  // 微页面组件分页用
  parsedGoods.goodsDetail.themeFeature = {
    uniqueKey: parsedGoods.pageParams.uniqueAlias + makeRandomString(8),
    componentsLength: 1,
  };

  assign(parsedGoods, {
    orderPromotion: promotionTag,
  });

  /* --------------------------
   * 活动信息
   --------------------------- */
  // 0元购 拼团
  if (luckyDrawGroup) {
    assign(marketActivity, {
      luckyDrawGroup: {
        activityId: luckyDrawGroup.id,
        activityName: luckyDrawGroup.name,
        level: luckyDrawGroup.level,
        type: luckyDrawGroup.type,
        buttonType: luckyDrawGroup.buttonType,
        couponName: luckyDrawGroup.couponName,
        beforeStartTime: luckyDrawGroup.beforeStartTime,
        endRemainTime: luckyDrawGroup.endRemainTime,
        startTime: luckyDrawGroup.startTime,
        endTime: luckyDrawGroup.endTime,
        goodsNum: luckyDrawGroup.goodsNum,
        joinNum: luckyDrawGroup.participantNum,
        shopId: luckyDrawGroup.shopId,
        solved: luckyDrawGroup.solved,
        sku: formatSkuTree(luckyDrawGroup.spu),
        ownGroup: luckyDrawGroup.ownGroup,
      },
    });
  }

  // 砍价0元购
  if (helpCut) {
    assign(marketActivity, {
      helpCut: {
        activityId: helpCut.activityId,
        sponsorId: helpCut.sponsorId,
        endRemainTime: helpCut.endRemainTime,
        endTime: helpCut.endTime,
        isFollowStart: helpCut.isFollowStart,
        level: helpCut.level,
        sku: formatSkuTree(helpCut.spu),
        startRemainTime: helpCut.startRemainTime || 0,
        startTime: helpCut.startTime,
        status: helpCut.status,
        title: helpCut.title,
        type: helpCut.type,
      },
    });
  }
  if (ladderGroupOn) {
    groupOn = ladderGroupOn || groupOn;
    groupOn.type = 'groupOn';
    // 阶梯团不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  // 拼团信息
  if (groupOn) {
    const ladderList = groupOn.ladderPrice || {};

    // 找出每个阶梯中价格最低的一项，组成一组阶梯，用于没有选中 sku 的时候用
    const minGroup = {};
    const prices = [];

    Object.keys(ladderList).forEach((key) => {
      const ladder = ladderList[key];

      ladder.forEach((item) => {
        const { scale } = item;
        prices.push(item.skuPrice);
        if (!minGroup[scale]) {
          minGroup[scale] = item;
          return;
        }

        minGroup[scale] =
          minGroup[scale].skuPrice > item.skuPrice ? item : minGroup[scale];
      });
    });

    ladderList[0] = Object.keys(minGroup).map((key) => minGroup[key]);

    assign(marketActivity, {
      groupOn: {
        // 活动alias
        groupAlias: groupOn.groupAlias,
        // 拼团类型 0：普通拼团 1：老带新拼团
        groupType: groupOn.groupType,
        // 活动剩余时间
        endRemainTime: groupOn.endRemainTime,
        // 活动剩余开始时间
        startRemainTime: groupOn.startRemainTime || 0,
        // 参团人数
        activityId: groupOn.activityId,
        // 按钮类型
        buttonType: groupOn.buttonType,
        level: groupOn.level,
        // 当前拼团活动凑团列表
        ongoingGroup: groupOn.ongoingGroup || [],
        // 更多拼团活动链接
        ongoingGroupsPath: groupOn.ongoingGroupsPath,
        // 拼团限购数据
        limit: {
          num: groupOn.quota,
          used: groupOn.quotaUsed,
          singleQuota: groupOn.perOrderLimit,
        },
        sku: formatSkuTree(groupOn.spu),
        joinNum: groupOn.joinNum,
        alertType: groupOn.alertType,
        type: groupOn.type,
        ladderList,
        ladderMinPrice: Math.min(...prices),
        ladderMaxPrice: Math.max(...prices),
      },
    });
  }

  // 团购
  if (tuan) {
    assign(marketActivity, {
      tuan: {
        allRules: makeTuanMonkey(tuan.allRules),
        endRemainTime: tuan.endRemainTime,
        endTime: tuan.endTime,
        id: tuan.id,
        isCash: tuan.isCash,
        isDelete: tuan.isDelete,
        level: tuan.level,
        percentage: tuan.percentage,
        returnMoney: tuan.returnMoney,
        rules: makeTuanMonkey(tuan.rules),
        soldCount: tuan.soldCount,
        limitedDays: tuan.limitedDays,
        sku: formatSkuTree(tuan.spu),
        startRemainTime: tuan.startRemainTime,
        startTime: tuan.startTime,
        state: tuan.state,
        type: tuan.type,
      },
    });
  }

  // 秒杀
  if (seckill) {
    assign(marketActivity, {
      seckill: {
        activityId: seckill.activityId,
        alias: seckill.alias,
        startAt: seckill.startAt,
        endAt: seckill.endAt,
        isCheckRight: seckill.isCheckRight,
        buyNeedBook: seckill.buyNeedBook,
        isDelete: seckill.isDelete,
        isPassCheck: seckill.isPassCheck,
        level: seckill.level,
        noticeTime: seckill.noticeTime,
        cancelTime: seckill.cancelTime,
        bookingNumText: seckill.bookingNumText,
        limit: {
          num: seckill.quota,
          used: seckill.quotaUsed,
        },
        sku: formatSkuTree(seckill.spu),
        tag: seckill.tag,
        title: seckill.title,
        type: seckill.type,
        questionId: seckill.questionId,
        useFollow: seckill.useFollow,
        useQuestion: seckill.useQuestion,
      },
    });
  }

  // 积分信息
  if (pointsExchange) {
    assign(marketActivity, {
      pointsExchange: {
        buyLimit: pointsExchange.buyLimit,
        id: pointsExchange.id,
        level: pointsExchange.level,
        limit: {
          num: pointsExchange.quotaNum,
          used: pointsExchange.quotaUsed,
        },
        sku: formatSkuTree(pointsExchange.spu),
        startAt: pointsExchange.startAt,
        endAt: pointsExchange.endAt,
        type: pointsExchange.type,
        // goods-info的显示单位，如金币、元、分
        points: pointsData,
      },
    });
    // 积分兑换不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  if (presentExchange) {
    assign(marketActivity, {
      presentExchange: {
        alias: presentExchange.alias,
        buyerId: presentExchange.buyerId,
        createdAt: presentExchange.createdAt,
        fansId: presentExchange.fansId,
        goodsId: presentExchange.goodsId,
        id: presentExchange.id,
        isDelete: presentExchange.isDelete,
        isReceive: presentExchange.isReceive,
        isView: presentExchange.isView,
        kdtId: presentExchange.kdtId,
        level: presentExchange.level,
        orderId: presentExchange.orderId,
        orderNo: presentExchange.orderNo,
        present: presentExchange.present,
        presentId: presentExchange.presentId,
        source: presentExchange.source,
        tourist: presentExchange.tourist,
        type: 'presentExchange',
      },
    });
    // 赠品不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  // 打包一口价
  if (bale) {
    assign(orderActivity, {
      bale: {
        id: bale.id,
        alias: bale.alias,
        num: bale.baleNum,
        // url: `${state.url.wap}/bale/${bale.alias}`,
        price: bale.balePrice,
      },
    });
  }

  // 限时折扣
  if (timelimitedDiscount) {
    assign(orderActivity, {
      timelimitedDiscount: {
        activityId: timelimitedDiscount.activityId,
        discount: timelimitedDiscount.discount,
        discountType: timelimitedDiscount.discountType,
        discountValue: timelimitedDiscount.discountValue,
        earseType: timelimitedDiscount.earseType,
        endAt: timelimitedDiscount.endAt,
        endRemainTime: timelimitedDiscount.endRemainTime,
        isAllowContinueBuy: timelimitedDiscount.isAllowContinueBuy,
        isStarted: timelimitedDiscount.isStarted,
        kdtId: timelimitedDiscount.kdtId,
        level: timelimitedDiscount.level,
        title: timelimitedDiscount.priceTitle,
        limit: {
          num: timelimitedDiscount.quota,
          used: timelimitedDiscount.quotaUsed,
        },
        sku: formatSkuTree(timelimitedDiscount.spu),
        startAt: timelimitedDiscount.startAt,
        type: 'timelimitedDiscount',
      },
    });
    assign(parsedGoods, {
      sku: formatSkuTree(timelimitedDiscount.spu),
    });
  }

  // 满减送
  if (meetReduce) {
    assign(orderActivity, {
      meetReduce: {
        // 满减送活动详情
        content: meetReduce.content,
        tag: meetReduce.tag,
        // 满减活动名称数组
        tags: meetReduce.tags,
      },
    });
  }

  // 供货商满减送
  if (supplierMeetReduce) {
    assign(orderActivity, {
      supplierMeetReduce: {
        content: supplierMeetReduce.content,
        tag: supplierMeetReduce.tag,
      },
    });
  }

  // 订单返现
  if (cashBack) {
    assign(orderActivity, {
      cashBack: {
        startTime: cashBack.startTime,
        endTime: cashBack.endTime,
        // 返现方式
        type: cashBack.cashbackMethod,
        // 返现限制
        limit: cashBack.cashbackLimit,
        // 返现区间最大值
        maxCashBack: cashBack.cashbackEnd,
      },
    });
  }

  // 优惠套餐
  if (packageBuy) {
    assign(orderActivity, {
      packageBuy: packageBuy.map((item) => ({
        // 商品参加的套餐总数
        count: item.count,
        // 是否有最大优惠金额，商品多规格的情况会存在价格区间
        hasMaxSavings: item.hasMaxSavings,
        id: item.id,
        idAlias: item.idAlias,
        level: item.level,
        pictureUrl: item.pictureUrl,
        planType: item.planType,
        price: item.price,
        // 优惠金额
        saveMoneyCn: item.saveMoneyCn,
        // 是否展示优惠金额
        showPreference: item.showPreference,
        title: item.title,
        type: item.type,
      })),
    });
  }

  // 加价购
  if (plusBuy) {
    const condition = +(plusBuy.threshold / 100).toFixed(2);
    const discounts = +(plusBuy.saveMoneyCn / 100).toFixed(2);
    assign(orderActivity, {
      plusBuy: {
        discounts,
        name: '换购',
        activityId: plusBuy.id,
        desc: `满${condition}元享优惠换购`,
        goodsList: plusBuy.goodsList.map((item) => ({
          imageUrl: item.thumbUrl,
          price: +(item.price / 100).toFixed(2),
          alias: item.alias,
          tag: '换购',
        })),
      },
    });
  }

  // 扫码会有或者会员折扣
  if (discount) {
    assign(orderActivity, {
      discount: {
        title: discount.priceTitle,
        type: discount.discountType,
        // 折扣
        discount: discount.discount,
        // 减免金额
        decrease: discount.decrease,
        // 展示卡相关逻辑
        recommendedPriceModel: discount.recommendedPriceModel || {},
      },
    });
    assign(parsedGoods, {
      sku: formatSkuTree(discount.spu),
    });
  }

  if (discountInvite) {
    assign(orderActivity, {
      discountInvite: {
        // 展示卡相关逻辑
        recommendedPriceModel: discountInvite.recommendedPriceModel || {},
      },
    });
  }

  // 包邮工具
  if (carriageDiscount) {
    assign(orderActivity, {
      carriageDiscount: {
        content: carriageDiscount.content,
        tag: carriageDiscount.tag,
        tags: carriageDiscount.tags,
      },
    });
  }

  // 定金膨胀
  if (depositExpansion) {
    assign(orderActivity, {
      depositExpansion: {
        activityId: depositExpansion.activityId,
        endAt: depositExpansion.endAt,
        endRemainTime: depositExpansion.endRemainTime,
        endTime: depositExpansion.endTime,
        startAt: depositExpansion.startAt,
        startTime: depositExpansion.startTime,
        type: 'depositExpansion',
      },
    });
    // 定金膨胀不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  // 预售
  if (presale) {
    // 定金膨胀skuId对应抵消金额
    let skus = [];
    let depositType = 'none';
    if (depositExpansion) {
      depositType = 'normal';
      skus = get(depositExpansion, 'skus', []);
    } else if (helpDepositExpansion) {
      depositType = 'help';
      skus = get(helpDepositExpansion, 'skuPriceOffers', []);
    }

    const depositExpansionMap = {};
    if (skus.length > 0) {
      skus.forEach((item) => {
        depositExpansionMap[item.skuId] = item;
      });
    }

    assign(goodsActivity, {
      presale: {
        // 预售类型 0：全款预售 1：定金预售
        type: presale.preSaleType,
        // 发货类型 0：定时发货 1：付款xx天后发货
        shipType: presale.etdType,
        // 定时发货时间
        shipStartTime: presale.etdStart,
        // 付款延迟发货时间
        shipDelayDays: presale.etdDays,
        // 定金比率
        depositRatio: presale.depositRatio,
        // 定金最小值
        minDeposit: presale.minDeposit,
        // 定金最大值
        maxDeposit: presale.maxDeposit,
        // 定金支付最早时间
        preStartTime: presale.preSaleStart,
        // 定金支付截止时间
        preEndTime: presale.preSaleEnd,
        // 尾款支付最早时间
        payStartTime: presale.balanceDueStart,
        // 尾款支付最晚时间
        payEndTime: presale.balanceDueEnd,
        // 定金膨胀skuId对应抵消金额
        skus,
        // 助力定金膨胀
        helpDepositExpansion,
        // 定金(膨胀)类型 none 不参加 normal 初始膨胀金 help 助力膨胀
        depositType,
        // 根据skuId计算出的可抵价格
        depositExpansionMap,
      },
    });
    // 预售不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  // 送礼商品通过url里带type=gift识别
  if (
    get(parsedGoods, 'pageParams.activityType', '').toUpperCase() === 'GIFT'
  ) {
    assign(goodsActivity, {
      gift: {},
    });
  }

  // 周期购
  if (periodbuy) {
    assign(goodsActivity, {
      periodbuy: {
        // 周期购描述
        description: periodbuy.description,
        // 配送周期 0：每天 1：每周 2：每月
        period: periodbuy.period,
        // 送达日期 周一至周日，可多选
        deliveryTimeList: periodbuy.deliverTime,
        // 规格周期
        periodSku: periodbuy.skuExtras,
        // 送达时间
        deliverTimeId: '',
      },
    });
    // 周期购不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  // 电子卡券
  if (virtualTicket) {
    assign(goodsActivity, {
      virtualTicket: {
        // 节假日是否可用，仅做展示用，实际不校验
        // 0：不可用，1：可用
        isHolidaysAvailable: virtualTicket.holidaysAvailable,
        // 卡券生效类型 0：立即生效 1：xx小时后生效 2：次日生效
        effectType: virtualTicket.effectiveType,
        // 生效延迟时间
        effectDelayTime: virtualTicket.effectiveDelayHours,
        // 卡券有效期类型 0：长期可用 1：xx天内可用 2：时间区间内可用
        validityType: virtualTicket.validityType,
        // 有效天数
        validityDays: virtualTicket.itemValidityDay,
        // 可用开始时间
        startDate: virtualTicket.itemValidityStart,
        // 可用结束时间
        endDate: virtualTicket.itemValidityEnd,
        // 使用说明，仅做展示用，实际不校验
        instructions: virtualTicket.instructions,
        // 预定时间 传给后端
        itemPreOrderTime: virtualTicket.itemPreOrderTime,
        // 预定时间 前端展示
        itemPreOrderTimeStr: virtualTicket.itemPreOrderTimeStr,
      },
    });
    // 电子卡券不支持商品属性
    unsupportItemSaleProp(parsedGoods);
  }

  return parsedGoods;
}

export { formatGoodsData };
