import money from '@youzan/weapp-utils/lib/money';
import args from '@youzan/weapp-utils/lib/args';
import openWebView from 'utils/open-web-view';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const app = getApp();
const p = 'packages';

/**
 * 将传入的分为单位的价格进行格式化
 * 返回 元 分 格式化以后的数据
 * @param {*} price 价格，分为单位
 */
export function splitPrice(price = 0) {
  const priceForYuan = money(price).toYuan();
  const [yuan, fen = '00'] = priceForYuan.split('.');

  return {
    yuan,
    fen,
    desc: priceForYuan,
  };
}

// 获取定金金额
// 定金金额大于0.1元，进行抹分，当计算后金额小于等于0.1元，精确到分，不足1分取1分
export function getDepositPrice(price, depositRatio) {
  if (typeof price === 'string') {
    price = +price * 100;
  }
  let depositPrice = price * depositRatio;
  // 如果定金金额大于0.1元
  if (depositPrice > 10) {
    depositPrice = Math.floor(depositPrice / 10) * 10;
  } else if (depositPrice >= 1) {
    // 当小于0.1元，精确到分，不足1分取1分
    depositPrice = Math.round(depositPrice);
  } else {
    depositPrice = 1;
  }
  return depositPrice;
}

// 弹窗报错提醒
export function showWarningModal(options) {
  const { title = '获取详情失败', content } = options;
  wx.showModal({
    title,
    content,
    showCancel: true,
    cancelText: '返回',
    confirmText: '进店逛逛',
    success: (res = {}) => {
      if (res.confirm) {
        wx.reLaunch({
          url: `/${p}/home/<USER>/index`,
        });
      } else {
        wx.navigateBack();
      }
    },
  });
}

export function cloneDeep(res = {}) {
  if (!res) {
    return res;
  }

  if (Array.isArray(res)) {
    return res.map((item) => {
      return cloneDeep(item);
    });
  }

  if (typeof res === 'object') {
    const result = {};
    Object.keys(res).forEach((key) => {
      result[key] = cloneDeep(res[key]);
    });
    return result;
  }

  return res;
}

export function doRedirectPage(
  { itemType: type, upgradedKdtId = 0, lockUrl, pluginsType, ...data },
  { sls = '' }
) {
  let url = '';

  if (upgradedKdtId) {
    app.trigger('update:youzan:kdtId', upgradedKdtId, {
      mark: '918',
    });
    wx.reLaunch({
      url: `/${p}/home/<USER>/index`,
    });
    return;
  }

  if (lockUrl) {
    openWebView('/v3/common/lock', {
      title: '提示',
    });

    return;
  }

  const formatType = +type;
  if (formatType === 20) {
    url = args.add(`/${p}/card/detail/index`, {
      alias: data.memberCardAlias,
    });
    if (sls) {
      url = addSalesmanParams({ url, sls });
    }
  }

  if (formatType === 31) {
    const { owlType, owlAlias, itemAlias } = data;

    if (owlType === 'column') {
      url = args.add(`/${p}/paidcontent/column/index`, {
        alias: owlAlias,
      });
    } else if (owlType === 'content') {
      url = args.add(`/${p}/paidcontent/content/index`, {
        alias: owlAlias,
      });
    } else {
      // 连锁切换店铺的情况下，kdtId会变，所以需要在方法调用的时候实时获取当前店铺kdtId
      const kdtId = app.getKdtId();
      const targetUrl = `https://h5.youzan.com/wscvis/edu/prod-detail?alias=${itemAlias}&kdt_id=${kdtId}`;
      url = args.add(`/${p}/edu/webview/index`, {
        targetUrl: encodeURIComponent(targetUrl),
      });
    }
  }

  if (pluginsType === 'COMMUNITY_GROUP_BUY') {
    url = `/${p}/groupbuying/buyer-trade/buying/index`;
  }
  wx.redirectTo({ url });
}

// 格式化价格
function formatPrice(price) {
  if (price && typeof price === 'number') {
    return (price / 100).toFixed(2);
  }
  return price;
}

// 参数sku为formatSkuData之后的数据
export function formatSkuPrice(skuData = {}, pointsName = '积分') {
  const {
    pointsPriceText = '',
    price = 0,
    minPrice = -1,
    maxPrice = -1,
    pointsPrice = -1,
    oldPrice = 0,
    oldMinPrice = -1,
    oldMaxPrice = -1,
  } = skuData;
  let showPrice = '';
  let showOldPrice = '';
  // 如果后端返回showPrice，则显示后端的数据
  if (pointsPriceText) {
    showPrice = pointsPriceText;
  } else if (pointsPrice > 0) {
    // 如果是单个积分价格
    showPrice = `${pointsPrice}${pointsName}`;
    if (price > 0) {
      showPrice = `${showPrice}+￥${formatPrice(price)}`;
    }
  } else if (maxPrice > minPrice && minPrice >= 0) {
    // 普通价格区间
    showPrice = `${formatPrice(minPrice)}-${formatPrice(maxPrice)}`;
  } else {
    // 非区间价格可能为0
    showPrice = `${formatPrice(price)}`;
  }
  // 原价比现价贵，则会展示
  if (oldPrice > price || oldMinPrice > minPrice) {
    if (oldMaxPrice > oldMinPrice) {
      showOldPrice = `${formatPrice(oldMinPrice)}-${formatPrice(oldMaxPrice)}`;
    } else if (oldPrice > 0) {
      // 无价格区间
      showOldPrice = `${formatPrice(oldPrice)}`;
    }
  } else {
    showOldPrice = '';
  }

  return {
    showPrice,
    showOldPrice,
  };
}

export const mergeStore = (srcStore = {}, destStore = {}) => {
  return {
    state: {
      ...srcStore.state,
      ...destStore.state,
    },
    getters: {
      ...srcStore.getters,
      ...destStore.getters,
    },
    actions: {
      ...srcStore.actions,
      ...destStore.actions,
    },
    mutations: {
      ...srcStore.mutations,
      ...destStore.mutations,
    },
  };
};
