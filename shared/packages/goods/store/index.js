import state from './state';
import mutations from './mutations';
import actions from './actions';
import getters from './getters';
import { mergeStore } from './utils/helpers';

import containerStore from './container-store';
import moduleStore from './modules';

const store = [
  {
    state,
    getters,
    actions,
    mutations,
  },
  moduleStore,
  containerStore,
].reduce((a, b) => mergeStore(a, b), {});

export {
  mergeStore,
  store,
};
