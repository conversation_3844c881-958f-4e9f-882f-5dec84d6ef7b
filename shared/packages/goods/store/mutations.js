import { makeRandomString } from '@youzan/weapp-utils/lib/str';
import { formatGoodsData } from './utils/parse';
import { addSalesmanParams } from 'shared/utils/salesman-params-handler';

const mutations = {
  // setInitData后数据会被setGoodsData刷新，只有pageParams的数据得以保留
  setInitData(state, data = {}) {
    const { pageParams = {} } = state;

    pageParams.alias = data.alias;
    pageParams.redirectCount = data.redirectCount || 0;
    // 商品唯一标识
    pageParams.uniqueAlias = data.alias + makeRandomString(8);
    pageParams.path = data.path;

    // init之后会到setGoodsData-formatGoodsData 转换为activityId/activityType
    pageParams.activityType = data.activityType;
    pageParams.activityId = data.activityId;
    pageParams.sls = data.sls;
    // 赠品
    pageParams.present = data.present;
  },

  setGoodsData(state, payload = {}) {
    const formattedData = formatGoodsData(state, payload);
    // Object.assign(state, formattedData);
    Object.keys(formattedData).forEach((key) => {
      // 不存在的数据字段会被覆盖
      state[key] = formattedData[key];
    });
    // 更新一下数据版本（时间）信息，以刷新 new Date() 问题
    state.v = +new Date();
  },

  // 设置分销员分享基础信息
  setSalesmanInfo(state, payload = {}) {
    const { seller, skuData } = payload;
    const { salesman = {}, pageParams = {} } = state;
    if (pageParams.path) {
      if (!salesman.alias) {
        salesman.shareLink = addSalesmanParams({ url: pageParams.path, sl: seller });
      } else {
        // 带了sls、alias已经
        salesman.shareLink = pageParams.path;
      }
    }
    salesman.alias = salesman.alias || seller;

    const { marketActivity, orderActivity } = state;
    const { luckyDrawGroup, helpCut, groupOn, seckill, points, present, tuan } = marketActivity;
    const { timelimitedDiscount } = orderActivity;

    let sku = state.originSku;
    if (timelimitedDiscount) {
      sku = state.sku;
    } else if (luckyDrawGroup || helpCut || groupOn || seckill || points || present || tuan) {
      sku = skuData;
    }
    const { minPrice, maxPrice, price } = sku || {};
    const { isVirtualTicket } = state.goods || {};
    const isRange = minPrice !== maxPrice;

    let realPrice;
    if (price && !isRange) {
      realPrice = +price * 100;
    } else if (price && isRange && maxPrice) {
      // 区间商品按最高价
      realPrice = maxPrice;
    }

    // 有营销活动以营销活动价格为准
    /**
     * 营销活动区间价以最高价
     */

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && state.priceDateRange) {
      return {
        currentPrice: state.priceDateRange
      };
    }
    state.salesman.goodsPrice = realPrice;
  },

  // 设置SKU价格日历基础信息
  setCalendarInfoData(state, payload = {}) {
    const { priceCalendarData = {} } = state;
    Object.assign(priceCalendarData, payload);
  },

  // 设置购物车数量
  setCartCount(state, count) {
    state.cartInfo.count = count || 0;
  },

  // 不同的活动sku可能不同，根据scene调整
  setSkuConfig(state, skuConfig) {
    state.skuConfig = {
      ...state.skuConfig,
      ...skuConfig
    };
  },

  setSkuExtraData() {
    // 虚函数
  },

  setSkuStockNum(state, num) {
    state.sku.stockNum = num;
  },
  // 显示分享
  showSharePop(state) {
    state.displayPop.sharePopShow = true;
  },

  hideSharePop(state) {
    state.displayPop.sharePopShow = false;
  },

  // 显示SKU浮窗
  showSkuPop(state) {
    state.displayPop.skuPopShow = true;
  },

  hideSkuPop(state) {
    state.displayPop.skuPopShow = false;
  },

  setShareParams(state, payload = {}) {
    state.shareParams = {
      ...state.shareParams,
      ...payload
    };
  },

  GET_SHOWCASE_COMPONENTS_SUCCESS(state, componentList = []) {
    if (componentList.length > 0) {
      state.goodsDetail.showcaseComponents = componentList.map((comp) => {
        if (comp.type === 'goods_template_split') {
          comp = state.goodsDetail.showcaseComponents[0];
        }
        comp.key = `${String(Math.random()).slice(0, 6)}-${comp.type}`;
        return comp;
      });
      // 微页面组件分页用
      state.goodsDetail.themeFeature = {
        uniqueKey: state.pageParams.uniqueAlias + makeRandomString(8),
        componentsLength: state.goodsDetail.showcaseComponents.length
      };
    }
  }
};

export default mutations;
