/**
 * 计算属性
 */
import { moment } from '@youzan/weapp-utils/lib/time';
import get from '@youzan/weapp-utils/lib/get';
import { numberToPrice, formatPrice } from '../common';
import { getDepositPrice } from './utils/helpers';
import { minBy, maxBy } from './utils/helpers/array';

export default {
  /* ================= 活动标题栏 ===================== */
  activityTitle() {
    // 虚函数
    return '';
  },

  activityLabel() {
    return '';
  },

  activityJoinNum() {
    return 0;
  },

  activityType(state) {
    return get(state, 'activityInfo.activityTypeName', '');
  },

  // 积分单位
  pointsUnit(state) {
    return get(state, 'marketActivity.pointsExchange.points', '');
  },

  /* ===================== 预售 相关 =================== */
  isPresale(state) {
    const presale = get(state, 'goodsActivity.presale', null);
    return !!presale;
  },

  // 0: 定时发货 1：付款几天后发货
  isShipAfterPay(state) {
    const presale = get(state, 'goodsActivity.presale', {});
    return presale.shipType === 1;
  },

  skuId(state, getters) {
    const { skuData } = getters;
    const { skuExtraData } = state;

    if (skuData.noneSku) {
      return skuData.collectionId;
    }

    if (skuExtraData.selectedSkuComb) {
      return skuExtraData.selectedSkuComb.id;
    }

    return null;
  },

  // 0：全款预售 1：定金预售
  isDepositPresale(state) {
    const presale = get(state, 'goodsActivity.presale', {});
    return presale.type === 1;
  },

  // 定金预售已结束
  isDepositPresaleOver(state, getters) {
    const presale = get(state, 'goodsActivity.presale', {});
    // 如果用户参加了助力的话，结束时间就变成尾款的结束时间
    if (!getters.isDepositPresaleHelpNotJoin && getters.isHelpDepositPresale) {
      return getters.isTailPayOver;
    }
    return presale.preEndTime < Date.now();
  },

  // 没参加助力活动
  isDepositPresaleHelpNotJoin(state, getters) {
    const joinActivityStatus = get(state, 'goodsActivity.presale.helpDepositExpansion.joinActivityStatus', '');
    return getters.isHelpDepositPresale && +joinActivityStatus === 0;
  },

  // 助力邀请开始状态joinActivityStatus: 0 未参与；1 开始助力 2 助力结束开始付尾款 3 参与完成
  isDepositPresaleHelpStart(state, getters) {
    const joinActivityStatus = get(state, 'goodsActivity.presale.helpDepositExpansion.joinActivityStatus', '');
    return getters.isHelpDepositPresale && +joinActivityStatus === 1;
  },

  // 开始付尾款
  isTailPayStart(state, getters) {
    const presale = get(state, 'goodsActivity.presale', {});
    const joinActivityStatus = get(state, 'goodsActivity.presale.helpDepositExpansion.joinActivityStatus', '');
    const isPaid = getters.isDepositPresaleHelpStart && presale.payStartTime < +new Date();
    return isPaid || joinActivityStatus === 2;
  },

  // 尾款支付完成
  isDepositPresaleHelpPayed(state, getters) {
    const joinActivityStatus = get(state, 'goodsActivity.presale.helpDepositExpansion.joinActivityStatus', '');
    return getters.isHelpDepositPresale && joinActivityStatus === 3;
  },

  isHelpDepositPresale(state, getters) {
    const presale = get(state, 'goodsActivity.presale', {});
    return getters.isDepositPresale && presale.depositType === 'help';
  },

  isTailPayOver(state) {
    const presale = get(state, 'goodsActivity.presale', null);

    return presale && presale.payEndTime < new Date();
  },

  // 预售倒计时
  presaleCountdown(state, getters) {
    const presale = get(state, 'goodsActivity.presale', null);
    let desc = '';
    let end = '';
    const now = Date.now();

    if (state.goods.forbidBuyReason === '活动未开始') {
      desc = '距开始仅剩';
      end = presale.preStartTime;
    } else {
      desc = '距结束仅剩';
      end = presale.preEndTime;

      // 如果参与了助力，那么结束时间就改成尾款时间
      if (!getters.isDepositPresaleHelpNotJoin && getters.isHelpDepositPresale) {
        end = presale.payEndTime;
      }

      if (end < now) {
        desc = '活动已结束';
        end = now;
      }
    }

    return {
      desc,
      start: now,
      end
    };
  },

  // 预售说明 - 预售拼团 底部bottom会用到
  presaleDesc(state, getters) {
    const activity = get(state, 'goodsActivity.presale', null);
    if (!activity) {
      return {};
    }
    let desc = '';
    let subDesc = '';
    // 0：全款预售 1：定金预售
    if (getters.isDepositPresale) {
      let suffix = '预售结束';
      let endTime = activity.preEndTime;

      // 助力结束时间 = 尾款支付时间
      if (getters.isDepositPresaleHelpStart) {
        suffix = '助力结束';
        endTime = activity.payStartTime;
      }

      desc = `${moment(endTime, 'MM.DD HH:mm:ss')}${suffix}`;
      // 0: 定时发货 1：付款几天后发货
      if (getters.isShipAfterPay) {
        subDesc = `尾款支付${activity.shipDelayDays}天后发货`;
      } else {
        subDesc = `${moment(activity.shipStartTime, 'MM.DD HH:mm:ss')}开始发货`;
      }
    } else if (getters.isShipAfterPay) {
      desc = `付款${activity.shipDelayDays}天后发货`;
    } else {
      desc = `${moment(activity.shipStartTime, 'MM.DD HH:mm:ss')}开始发货`;
    }
    return {
      desc,
      subDesc,
    };
  },

  // 限时折扣倒计时
  timelimitedDiscountCountdown(state) {
    const timelimitedDiscount = get(state, 'orderActivity.timelimitedDiscount', {});
    const { startAt, endAt } = timelimitedDiscount;
    let desc = '';
    let end = '';
    const now = +new Date();
    if (now < startAt) {
      desc = '距开始仅剩';
      end = startAt;
    } else if (endAt > now) {
      desc = '距结束仅剩';
      end = endAt;
    } else {
      desc = '活动已结束';
      end = now;
    }
    return {
      desc,
      start: now,
      end
    };
  },

  // activity-banner / goods-info会用到
  countdown(state, getters) {
    const timelimitedDiscount = get(state, 'orderActivity.timelimitedDiscount', null);
    if (timelimitedDiscount) {
      return getters.timelimitedDiscountCountdown;
    }
    // 如果是定金预售，则显示预售倒计时
    if (getters.isDepositPresale) {
      return getters.presaleCountdown;
    }
    return {};
  },

  depositPrice(state) {
    const presale = state.goodsActivity.presale;
    if (!presale) {
      return {};
    }
    const { minDeposit, maxDeposit } = presale;

    if (maxDeposit === minDeposit) {
      return numberToPrice(minDeposit);
    }
    return formatPrice({ min: minDeposit, max: maxDeposit }, ' - ');
  },

  // 定金尾款
  tailPrice(state, getters) {
    const presale = state.goodsActivity.presale;
    if (!presale) {
      return {};
    }

    const { minPrice, maxPrice } = state.sku;
    const { skuData = {} } = getters;
    const { list: skuList = [] } = skuData;
    const { skuMinMaxOffer } = getters;
    const { skus: presaleSkus = [] } = presale;
    let { minDeposit, maxDeposit } = presale;

    /*
     助力膨胀数据类型： [{"deposit_offer":300,"helpOffer":[300,600],"sku_id":1065478}]
     skuData: [{ oldPrice: 10000}]
    */
    const { isHelpDepositPresale } = getters;
    let minTailPrice = minPrice - minDeposit;
    let maxTailPrice = maxPrice - maxDeposit;

    if (skuMinMaxOffer) {
      if (isHelpDepositPresale && skuList.length > 0) {
        const allTailPrice = [];
        skuList.forEach((sku, index) => {
          allTailPrice.push(sku.oldPrice - Math.min(...presaleSkus[index].helpOffer));
          allTailPrice.push(sku.oldPrice - Math.max(...presaleSkus[index].helpOffer));
        });

        minDeposit = Math.min(...allTailPrice);
        maxDeposit = Math.max(...allTailPrice);
        minTailPrice = minDeposit;
        maxTailPrice = maxDeposit;
      } else {
        const { offerMinOffset, offerMaxOffset } = skuMinMaxOffer;
        minDeposit = offerMinOffset;
        maxDeposit = offerMaxOffset;
        minTailPrice = minPrice - offerMinOffset;
        maxTailPrice = maxPrice - offerMaxOffset;
      }
    }

    if (minDeposit === maxDeposit) {
      return numberToPrice(minTailPrice);
    }

    return formatPrice({ min: minTailPrice, max: maxTailPrice }, ' - ');
  },

  // 可抵消价格范围
  offsetPriceRange(state, getters) {
    const { skuMinMaxOffer, isHelpDepositPresale } = getters;

    if (!skuMinMaxOffer) {
      return '';
    }

    const { offerMinOffset, offerMaxOffset } = skuMinMaxOffer;
    const prefix = isHelpDepositPresale ? '最高可抵' : '可抵';

    if (offerMinOffset === offerMaxOffset || isHelpDepositPresale) {
      return `${prefix}¥${numberToPrice(offerMaxOffset)}`;
    }
    return `${prefix}¥${numberToPrice(offerMinOffset)} - ${numberToPrice(offerMaxOffset)}`;
  },

  selectedSkuDeposit(state, getters) {
    const presale = state.goodsActivity.presale;
    if (!presale) {
      return '';
    }

    const selectedSkuComb = get(state, 'skuExtraData.selectedSkuComb', '');
    const selectedSkuId = selectedSkuComb.id || '';
    const { skus: presaleSkus = [] } = presale;
    const { isHelpDepositPresale } = getters;

    if (isHelpDepositPresale) {
      const targetPresaleSku = presaleSkus.find(sku => sku.skuId === selectedSkuId) || {};
      return `最高可抵${formatPrice(Math.max(...targetPresaleSku.helpOffer || []))}`;
    }

    return '';
  },

  skuMinMaxOffer(state, getters) {
    const presale = state.goodsActivity.presale;
    if (!presale) return '';
    const { skus } = presale;

    let offerMinOffset = 0;
    let offerMaxOffset = 0;

    if (skus.length > 0) {
      // 助力膨胀数据类型： [{"deposit_offer":300,"helpOffer":[300,600],"sku_id":1065478}]
      if (getters.isHelpDepositPresale) {
        const initValue = skus[0].depositOffer;

        offerMinOffset = skus.reduce((prev, currentItem) => {
          return Math.min(prev, Math.min(...currentItem.helpOffer));
        }, initValue);

        offerMaxOffset = skus.reduce((prev, currentItem) => {
          return Math.max(prev, Math.max(...currentItem.helpOffer));
        }, initValue);
      } else {
        offerMinOffset = minBy(skus, items => items.depositOffer).depositOffer;
        offerMaxOffset = maxBy(skus, items => items.depositOffer).depositOffer;
      }

      return {
        offerMinOffset,
        offerMaxOffset
      };
    }

    return null;
  },

  depositPaymentTime(state) {
    const { payStartTime, payEndTime } = get(state, 'goodsActivity.presale', {});
    const beginTimeStr = moment(payStartTime, 'YYYY.MM.DD HH:mm:ss');
    const endTimeStr = moment(payEndTime, 'YYYY.MM.DD HH:mm:ss');

    return `尾款：${beginTimeStr} - ${endTimeStr}`;
  },

  // 有些商品活动可能会覆盖sku
  // ?? sku 重写弹起价格重置问题
  skuData(state, getters) {
    if (getters.saasSkuData) {
      return getters.saasSkuData;
    }
    const { orderActivity = {}, goodsActivity = {} } = state;
    if (orderActivity.timelimitedDiscount) {
      return orderActivity.timelimitedDiscount.sku;
    }
    // 定金实物预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      const depositRatio = goodsActivity.presale.depositRatio / 100;
      const sku = state.sku;
      const { depositExpansionMap = {}, skus = [] } = goodsActivity.presale;
      let depositOffer = '';
      if (sku.noneSku && skus.length === 1) {
        if (skus[0] && skus[0].depositOffer > 0) {
          depositOffer = (+skus[0].depositOffer / 100).toFixed(2, 10) || '';
        }
      }
      const depositPrice = getDepositPrice(sku.price, depositRatio);
      if (sku.list) {
        return {
          ...sku,
          list: sku.list.map(item => {
            let depositOffer = get(depositExpansionMap[item.id], 'depositOffer', 0);
            if (+depositOffer > 0) {
              depositOffer = (+depositOffer / 100).toFixed(2, 10);
            } else {
              depositOffer = '';
            }
            return {
              ...item,
              depositOffer,
              oldPrice: item.price,
              price: getDepositPrice(item.price, depositRatio),
            };
          }),
          depositOffer,
          oldPrice: sku.price,
          price: getters.depositPrice,
          minPrice: depositPrice,
          maxPrice: depositPrice,
        };
      }
    }
    return state.sku;
  },

  activitySkuData(state) {
    const { orderActivity = {} } = state;
    if (orderActivity.timelimitedDiscount) {
      return orderActivity.timelimitedDiscount.sku;
    }
    return state.sku;
  },

  /* ====================== 虚拟商品-电子卡券 =================== */
  virtualAvailableInfo(state) {
    const holidayList = ['节假日不可用', '节假日可用'];
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    return holidayList[virtualTicket.isHolidaysAvailable];
  },

  virtualValidPeriod(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const {
      endDate, startDate, validityType, validityDays
    } = virtualTicket;
    const startDateStr = moment(startDate, 'YYYY年MM月DD日');
    const endDateStr = moment(endDate, 'YYYY年MM月DD日');
    const periodMap = [
      '生效时间起长期可用',
      `生效时间起${validityDays}天内可用`,
      `在${startDateStr}-${endDateStr}内可用`,
      '在所选使用日期内当天可用'
    ];

    return periodMap[validityType];
  },

  virtualEffectTime(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const { effectType, effectDelayTime } = virtualTicket;
    const effectTimeMap = [
      '付款完成后立即生效',
      `付款完成${effectDelayTime}小时后生效`,
      '付款完成后次日生效'
    ];

    return effectTimeMap[effectType];
  },

  // 立即生效则不显示生效时间
  hideVirtualEffectTime(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const { effectType } = virtualTicket;
    return effectType === 0;
  },

  virtualInstruction(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const { instructions = '' } = virtualTicket;
    return instructions.replace(/\n/g, '<br>');
  },

  virtualRefundDocument(state) {
    const { isSupport, type, interval } = state.refund;
    if (!isSupport) {
      return '不支持申请退款';
    }
    if (type === 0) {
      return '未使用卡券随时可申请退款';
    }
    if (type === 1) {
      const day = Math.floor(interval / 24 / 60 / 60 / 1000);
      const left = interval % (24 * 60 * 60 * 1000);
      const hour = Math.floor(left / 60 / 60 / 1000);
      const dayStr = day ? `${day}天` : '';
      const hourStr = hour ? `${hour}小时` : '';
      return `未使用卡券在过期前${dayStr + hourStr}随时可以申请退款`;
    }
  },

  // presale end

  showAvailableStores(state) {
    return state.multistore.salableStores.length > 0;
  },

  // 多网点开启的情况下，如果商家关闭了网点切换开关，这里的分店入口也要隐藏
  showMultiStoreInfo(state) {
    const {
      // env: { isNewHopeKdt },
      // shopConfig: { hasPhysicalStore },
      multistore: { id, openMultiStoreSwitch, hideStore }
    } = state;
    // 新希望店铺隐藏线下门店入口
    // return id ? !hideStore && openMultiStoreSwitch : hasPhysicalStore && !isNewHopeKdt;
    // 先隐藏线下门店入口
    return id && !hideStore && openMultiStoreSwitch;
  },

  showAddressContent(state, getters) {
    return getters.addressContent.length > 0;
  },

  // 零售店铺才会有
  addressContent(state) {
    return state.retail.address;
  },

  // 网点是否售罄标识
  isMultiStoreSoldout(state, getters) {
    const { id } = state.multistore;
    const { goodsStockNum } = getters;
    if (typeof id === 'number' && typeof goodsStockNum === 'number') {
      return id > 0 && goodsStockNum === 0;
    }
    return false;
  },

  showSkuTag(state) {
    const discount = get(state, 'goodsActivity.discount', {});
    return Object.keys(discount) > 0;
  },

  activityIntro(state) {
    const bale = get(state, 'orderActivity.bale', {});
    const { price, num, url } = bale;
    return {
      title: `${numberToPrice(price)}元任选${num}件`,
      actionText: '去凑单',
      link: url
    };
  },

  cashBackRuleStr(state) {
    const cashBack = get(state, 'orderActivity.cashBack', {});
    const {
      type, limit, startTime, endTime, maxCashBack
    } = cashBack;
    let str = '';

    if (maxCashBack) {
      const limitStr = limit === 1 ? '首' : `前${limit}`;
      const typeStr = type === 'fixed' ? '可获得' : '可随机获得';
      str = `${startTime}至${endTime}期间，在本店${limitStr}笔订单支付成功，${typeStr}返现；`;
    }

    return str;
  },

  showCustomerReviews(state) {
    return !!state.shopConfig.showCustomerReviews;
  },

  // 是否开启买家秀
  showBuyerShows(state) {
    return !!state.shopConfig.showBuyerShows;
  },
  // 是否展示视频
  showGoodsVideo(state) {
    return !!state.video.videoUrl;
  },

  showOriginPrice(state, getters) {
    const { kdtfrom } = state.env;
    const { price, isDepositPresale } = getters;

    return price.originPrice && !(kdtfrom === 'wxd' || isDepositPresale);
  },

  hideCountdown() {
    return false;
  },

  showDatePickerBar() {
    return false;
  },

  // FIXME 逻辑怎么处理待商榷,是否是微小店
  isWxd(state) {
    return state.env.platform === 'youzanwxd';
  },

  // 是否是零售店铺
  isRetailShop(state) {
    return state.shop.shopType === 7;
  },

  // 是否是外链商品
  isOutLinkGoods(state) {
    return state.goods.buyWay === 0;
  },

  isShopClosed() {
    // 小程序上自动有店铺休息等提示，无需额外添加
    return false;
  },

  kdtId(state) {
    return state.shop.kdtId;
  },

  alias(state) {
    return state.goods.alias;
  },

  // 库存
  goodsStockNum(state) {
    return get(state, 'sku.stockNum', 0);
  },

  // 已售
  goodsSoldNum(state) {
    return get(state, 'sku.soldNum', 0);
  },

  goodsTipCashBackDesc() {
    // 虚函数，团购定金返现说明文案
    return '';
  },

  buttomImButtonText(state) {
    const { display: { imButtonText = '客服', showDefaultImButtonText } } = state;

    return showDefaultImButtonText ? '客服' : imButtonText;
  },

  buttomBuyButtonText(state) {
    const { display: { buyButtonText = '立即购买', showDefaultBuyButtonText } } = state;

    return showDefaultBuyButtonText ? '立即购买' : buyButtonText;
  },

  nowDate(state) {
    // 获取一下版本值，以刷新当前时间状态
    /* eslint-disable-next-line */
    const { v } = state;
    return +new Date();
  },

  // 链接上需要带ump信息才能展示营销信息，如秒杀、砍价0元购、0元抽奖团，现在主要用于商品属性价格计算
  activityInfoForPrice() {
    return {};
  },
};
