// import { formatSkuPrice } from '../utils/helpers';
import { or } from 'shared/packages/goods/common';
import { SKU_SCENE } from 'shared/packages/goods/common/constant';
import { calculatePrice } from '../utils/fetch';

const timeOutMap = {};

const debounce = (key, delta, cb, cancel) => {
  if (timeOutMap[key]) {
    clearTimeout(timeOutMap[key].timer);
    timeOutMap[key].cancel && timeOutMap[key].cancel();
    delete timeOutMap[key];
  }
  timeOutMap[key] = {
    timer: setTimeout(() => {
      cb && cb();
    }, delta),
    cancel,
  };
};

const debouncePromise = key => {
  return new Promise((resolve, reject) => {
    debounce(key, 500, () => {
      resolve();
    }, () => {
      reject(new IngoreError('cancel'));
    });
  });
};

class IngoreError extends Error {}

const state = {
  skuPrice: {
    price: '',
    oldPrice: '',
  },
};

const mutations = {
  SET_SKU_PRICE(state, payload) {
    state.skuPrice = {
      price: '',
      oldPrice: '',
      ...payload,
    };
  },
};

const calList = [
  'propPrice',
  'afterPrice',
];

let v = 0;
const priceData = {
  propPrice: {
    data: null,
    v: 0,
  },
  afterPrice: {
    data: null,
    v: 0,
  },
};

const actions = {
  startCalculate({ dispatch, getters }, value) {
    const { selectedSKuAndProp } = getters;
    let step = calList.indexOf(value);
    if (step === -1) {
      step = 0;
    }
    const nowV = v++;
    const baseData = step === 0 ? selectedSKuAndProp : priceData[calList[step - 1]].data;
    let next = dispatch(calList[step], {
      v: nowV,
      data: baseData,
    });
    for (let i = step + 1; i < calList.length; i++) {
      priceData[calList[i]].v = nowV;
      next = next.then(() => {
        return dispatch(calList[i], priceData[calList[i - 1]]);
      });
    }
    next.catch(() => {
      // 处理出错情况
    });
  },
  formatCalculateSkuPrice({ commit, getters }, type) {
    // const { pointsName = '' } = state.goods;
    let skuInfo = getters.skuData;
    if (type && priceData[type] && priceData[type].data && priceData[type].data.id) {
      skuInfo = priceData[type].data;
    }
    const { price, oldPrice, totalPrice, totalOldPrice } = skuInfo;
    // const { showPrice, showOldPrice } = formatSkuPrice({
    //   ...skuInfo,
    //   price: or(totalPrice, price),
    //   pointsPrice: or(totalPointsPrice, pointsPrice),
    //   oldPrice: or(totalOldPrice, oldPrice),
    // }, pointsName);
    // commit('SET_SKU_PRICE', {
    //   price: showPrice,
    //   oldPrice: showOldPrice,
    // });
    commit('setSkuExtraData', {
      priceData: {
        showPrice: or(totalPrice, price),
        showOldPrice: or(totalOldPrice, oldPrice),
      },
    });
  },
  propPrice({ dispatch, getters }, payload) {
    priceData.propPrice.v = payload.v;
    if (payload.data && payload.data.id) {
      const { price = 0, oldPrice = -1, pointsPrice = 0, propertyPrice = 0 } = payload.data;
      const mergeData = {
        totalPrice: price + propertyPrice,
        totalPointsPrice: pointsPrice,
      };
      if (oldPrice > -1 && typeof oldPrice === 'number') {
        mergeData.totalOldPrice = oldPrice + propertyPrice;
      }
      priceData.propPrice.data = {
        ...payload.data,
        ...mergeData,
      };
      if (getters.isPropSync) {
        return debouncePromise('propPrice').then(() => {
          return dispatch('syncPropPrice', priceData.propPrice)
            .then(data => {
              if (data.v !== priceData.propPrice.v) {
                throw new IngoreError('version');
              } else {
                priceData.propPrice.data = {
                  ...priceData.propPrice.data,
                  ...data.data,
                };
                dispatch('formatCalculateSkuPrice', 'propPrice');
              }
              return Promise.resolve();
            });
        });
      }
      dispatch('formatCalculateSkuPrice', 'propPrice');
    } else {
      priceData.propPrice.data = null;
      dispatch('formatCalculateSkuPrice');
    }
    return Promise.resolve();
  },
  afterPrice() {
    // TODO
  },
  syncPropPrice({ state, getters }, payload) {
    const { data: skuComb, v } = payload;
    const mergeData = {
      v,
      data: {},
    };
    if (!skuComb || !skuComb.id) {
      return Promise.resolve(mergeData);
    }
    return calculatePrice({
      itemId: state.goods.id,
      kdtId: state.shop.kdtId,
      skuId: skuComb.id,
      itemSalePropList: JSON.stringify(skuComb.properties),
      activity: JSON.stringify(getters.activityInfoForPrice),
    }).then(data => {
      if (data && (typeof data.umpTotalPrice !== 'undefined' || typeof data.pointNum !== 'undefined')) {
        mergeData.data = {
          totalPrice: data.umpTotalPrice || 0,
          totalPointsPrice: data.pointNum || 0,
        };
      }
      return Promise.resolve(mergeData);
    }).catch(() => {
      return Promise.resolve(mergeData);
    });
  },
};

const getters = {
  selectedSKuAndProp(state, getters) {
    const { skuExtraData, goods } = state;
    const { itemSalePropList = [] } = goods;
    const { skuData = {} } = getters;
    const { selectedSkuComb = null } = skuExtraData;
    const { noneSku } = skuData;
    let info = null;
    if (selectedSkuComb) {
      // 已经选择规格和属性的情况
      info = selectedSkuComb;
    } else if (noneSku && itemSalePropList.length < 1) {
      // 完全没有规格和属性的情况
      info = {
        id: skuData.collectionId,
        price: skuData.price,
        oldPrice: skuData.oldPrice,
        pointsPrice: skuData.pointsPrice,
        pointsPriceText: skuData.pointsPriceText,
        stockNum: skuData.stockNum,
        propertyPrice: 0,
      };
    }
    if (info && typeof info.price === 'string') {
      info.price = Math.round(info.price * 100);
    }
    if (info && typeof info.oldPrice === 'string' && skuData.noneSku) {
      info.oldPrice = Math.round(info.oldPrice * 100);
    }
    return info;
  },
  isPropSync(state, getters) {
    const { selectedSKuAndProp } = getters;
    if (selectedSKuAndProp && selectedSKuAndProp.propertyPrice > 0) {
      const { skuConfig = {}, orderActivity = {} } = state;
      const skuScene = skuConfig.skuScene || '';
      const { timelimitedDiscount, discount } = orderActivity;
      // 营销购买、积分兑换、限时折扣、会员折扣、会员卡之类的需要调用接口计算最终价格
      if (skuScene === SKU_SCENE.ACT_BUY || skuScene === SKU_SCENE.POINTS_BUY
        || timelimitedDiscount || discount
      ) {
        return true;
      }
    }
    return false;
  },
};

export default { state, actions, getters, mutations };
