import { SKU_SCENE } from 'shared/packages/goods/common/constant';
import { getInstallmentPrice } from '../utils/fetch';
import money from '@youzan/weapp-utils/lib/money';

const caches = {};

export default {
  state: {
    installment: {},
    noInstallmentOrder: false,
  },

  getters: {
    showInstallmentSku(state) {
      return state.goods.isInstallment && state.skuConfig.skuScene !== SKU_SCENE.ADD_CART;
    },
  },

  mutations: {
    SET_INSTALLMENT(state, payload = {}) {
      const keys = Object.keys(payload);
      const installment = keys.reduce((res, key) => {
        res.push({
          key,
          val: money(payload[key]).toYuan()
        });
        return res;
      }, []);
      state.installment = installment;
    },

    SET_INSTALLMENT_COUNT(state, count) {
      const rerestIntallmentable = state.skuConfig.selectedInstallmentPeriod === count;
      state.skuConfig = {
        ...state.skuConfig,
        selectedInstallmentPeriod: rerestIntallmentable ? 0 : count,
      };
    },

    REST_INSTALLMENT_COUNT(state) {
      state.skuConfig = {
        ...state.skuConfig,
        installmentRate: 0,
        selectedInstallmentPeriod: 0
      };
    },

    SET_INSTALLMENT_ORDER(state, isGrey) {
      state.noInstallmentOrder = isGrey;
    },
  },

  actions: {
    fetchInstallentPrice({ state, commit, getters }, selectedSkuComb) {
      const { goods } = state;
      const { skuData } = getters;

      const query = {
        goodsId: goods.id,
        kdtId: getters.kdtId
      };

      if (skuData.noneSku) {
        query.skuId = skuData.collectionId;
        query.price = skuData.maxPrice;
      } else {
        if (!selectedSkuComb) {
          const list = skuData.list.map(item => ({ id: item.id, price: item.price }));

          list.sort((a, b) => {
            return a.price > b.price ? 1 : -1;
          });
          selectedSkuComb = list[0];
        }

        query.skuId = selectedSkuComb.id;
        query.price = selectedSkuComb.price;
      }

      const { skuScene } = state.skuConfig;
      const installment = caches.installment = caches.installment || {};
      const cahceKey = skuScene + query.skuId;
      commit('REST_INSTALLMENT_COUNT');
      let next = Promise.resolve(installment[cahceKey]);

      if (!installment[cahceKey]) {
        next = getInstallmentPrice(query)
          .then(data => {
            installment[cahceKey] = data;
            return data;
          });
      }

      return next.then(({ installmentRate, isGrey, instalmentPrices = {} }) => {
        commit('setSkuConfig', { installmentRate });
        const stepperCount = state.skuConfig.stepperCount || 1;

        instalmentPrices = Object.keys(instalmentPrices).reduce((res, key) => {
          res[key] = instalmentPrices[key] * stepperCount;
          return res;
        }, {});

        commit('SET_INSTALLMENT', instalmentPrices);
        commit('SET_INSTALLMENT_ORDER', isGrey);
      });
    },
    calcInstallmentPrices({ commit, state, getters }, value) {
      const { skuData } = getters;
      const { selectedSkuComb = {} } = state.skuExtraData;
      let selectedSkuId = selectedSkuComb && selectedSkuComb.id;
      if (skuData.noneSku) {
        selectedSkuId = skuData.collectionId;
      }

      const { skuScene } = state.skuConfig;
      const cahceKey = skuScene + selectedSkuId;

      const { instalmentPrices } = caches.installment[cahceKey] || {};

      if (!selectedSkuId || !instalmentPrices) return;

      const changedPrices = Object.keys(instalmentPrices)
        .reduce((res, key) => {
          res[key] = Number((instalmentPrices[key] * value).toFixed(2));

          return res;
        }, {});

      commit('SET_INSTALLMENT', changedPrices);
    },
  }
};

