import { splitPrice } from '../utils/helpers';
import { SKU_SCENE } from '../../common/constant';

const componentObj = {
  GoodsInfo: 'goodsInfo',
  GoodsBottom: 'goodsBottom',
};

const state = {
  saas: {
    goodsInfo: {
      display: true,
      properties: {},
    },
    goodsBottom: {
      display: true,
      properties: {},
    },
    // 缓存SKU信息
    skuInfos: {},
  },
};

const getters = {
  saasGoodsInfoShow(state) {
    return state.saas.goodsInfo.display;
  },
  saasGoodsTitle(state) {
    return state.saas.goodsInfo.properties.goodsTitle;
  },
  saasGoodsSubTitle(state) {
    return state.saas.goodsInfo.properties.goodsSubTitle;
  },
  saasGoodsPriceTag(state) {
    return state.saas.goodsInfo.properties.goodsPriceTag;
  },
  saasGoodsPrice(state) {
    let { minPrice = -1, maxPrice = -1, originPrice = '' } = state.saas.goodsInfo.properties;
    if (minPrice >= 0) {
      if (maxPrice < 0) {
        maxPrice = minPrice;
      }
      return {
        isRange: maxPrice !== minPrice,
        minPriceData: splitPrice(minPrice),
        maxPriceData: splitPrice(maxPrice),
        originPrice,
      };
    }
    return null;
  },
  saasGoodsBottomShow(state) {
    return state.saas.goodsBottom.display;
  },
  saasIsSaasSku(state) {
    return state.skuConfig.skuScene === SKU_SCENE.SAAS || state.saas.skuInfos.useSassAddCart;
  },
  saasSkuPriceTag(state, getters) {
    return getters.saasIsSaasSku ? state.saas.skuInfos.skuPriceTag : '';
  },
  saasSkuData(state, getters) {
    return getters.saasIsSaasSku ? state.saas.skuInfos.sku : null;
  },
  saasSkuExtensions(state) {
    return getters.saasIsSaasSku ? state.saas.skuInfos.extensions : {};
  },
  cloudOrderExt(state) {
    return getters.saasIsSaasSku ? state.saas.skuInfos.cloudOrderExt : '';
  },
};

const actions = {

};

const mutations = {
  saasUpdateComponent(state, payload = {}) {
    const { name = '', display, properties = {} } = payload;
    const key = componentObj[name];
    if (key) {
      state.saas[key] = {
        display,
        properties,
      };
    }
  },
  saasSkuCache(state, payload = {}) {
    const {
      skuPriceTag, sku, cloudOrderExt, extensions = {},
      useSassAddCart
    } = payload;
    state.saas.skuInfos = {
      skuPriceTag,
      sku,
      cloudOrderExt,
      extensions,
      useSassAddCart
    };
  },
};

export default {
  state, getters, actions, mutations,
};
