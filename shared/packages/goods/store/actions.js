// 点击底部按钮的场景，如立即购买、购物车等
import get from '@youzan/weapp-utils/lib/get';
import { SKU_SCENE, ACTVITY_TYPE_ID_MAP } from 'shared/packages/goods/common/constant';
import spm from 'shared/utils/spm';
import {
  getGoodsDetail,
  getShowcaseComponents,
  getCartCount,
} from './utils/fetch';

const app = getApp();

const actions = {
  // 获取商品列表
  fetchGoods({ state, commit, dispatch }, { pageType = '', skipShopInfo = false, beforeSetGoodsCheck }) {
    const { pageParams = {} } = state;
    return getGoodsDetail({
      ...pageParams,
      // pageType 为 seckill 需要特殊处理
      pageType
    }, { skipShopInfo })
      .then((res) => {
        if (beforeSetGoodsCheck && !beforeSetGoodsCheck(res)) {
          return Promise.reject();
        }

        commit('setGoodsData', res);
        wx.setNavigationBarTitle({
          title: get(res, 'goods.goods.title', '商品详情'),
        });
        dispatch('getShowcaseComponents');
        return res;
      });
  },

  getShowcaseComponents({ getters, commit }) {
    getShowcaseComponents(getters.alias)
      .then(res => {
        commit('GET_SHOWCASE_COMPONENTS_SUCCESS', res.components);
      })
      .catch(err => {
        console.log(err.msg);
      });
  },

  setSalesmanData({ getters, commit }, shareData) {
    const { activitySkuData: skuData } = getters;
    commit('setSalesmanInfo', {
      seller: shareData.seller,
      skuData
    });
  },

  // 购物车商品数量获取
  fetchCartCount({ commit }) {
    getCartCount().then((res = {}) => {
      const { value } = res;
      if (typeof value === 'number') {
        console.log('get cart count', value);
        commit('setCartCount', value);
      }
    });
  },

  updateSkuConfig() {
    // 虚函数
    // 根据skuScene，更新skuConfig
  },

  updateSkuExtraData() {
    // 虚函数
    // 根据skuScene，更新skuExtraData
  },

  // 显示SKU浮窗
  showSkuPop({
    state, commit, dispatch,
  }, event = null) {
    if (!event || !event.skuScene) {
      event = {
        ...event,
        skuScene: SKU_SCENE.SEL_SKU,
      };
    }

    let next = Promise.resolve();

    // 分期购买，无规格商品不触发 selectsku
    if (state.goods.isInstallment && event.skuScene) {
      next = dispatch('fetchInstallentPrice');
    }

    if (event) {
      // 这里有先后顺序，extraData会依赖config中的参数，判断skuData
      dispatch('updateSkuConfig', event);
      dispatch('updateSkuExtraData', event);
    }

    dispatch('startCalculate');

    // 存在特定场景才显示sku，接口挂了也要显示sku
    next.catch(() => {}).then(() => commit('showSkuPop'));
  },

  hideSkuPop({ commit }) {
    commit('hideSkuPop');
  },

  // ump 信息打点
  setUmpLogger({ state }) {
    const { marketActivity = {}, orderActivity = {} } = state;

    const umpParams = [];

    Object.keys(marketActivity).forEach((activityName = {}) => {
      const activity = marketActivity[activityName];
      const activityId = activity.id || activity.activityId;
      umpParams.push({
        act_id: `${activityId}`,
        act_type: ACTVITY_TYPE_ID_MAP[activityName],
      });
    });

    Object.keys(orderActivity).forEach((activityName = {}) => {
      let activity = orderActivity[activityName];
      if (activityName === 'packageBuy') {
        activity.forEach(v => {
          umpParams.push({
            act_type: '7',
            act_id: String(v.id),
          });
        });
      } else {
        if (Array.isArray(activity)) {
          activity = activity[0] || {};
        }
        const activityId = activity.id || activity.activityId;
        if (activityId) {
          umpParams.push({
            act_id: `${activityId}`,
            act_type: ACTVITY_TYPE_ID_MAP[activityName],
          });
        }
      }
    });

    if (umpParams.length > 0) {
      app.logger && app.logger.log({
        et: 'custom',
        ei: 'view_goods',
        spm: spm.getSpm(),
        params: {
          ump_params: umpParams
        },
        si: app.getKdtId()
      });
    }
  },
};

export default actions;
