import { mergeStore } from './utils/helpers';

import activityBannerStore from '../containers/activity-banner/store';
import goodsInfoStore from '../containers/goods-info/store';
import recommendStore from '../containers/recommend-store/store';
import orderPreferenceStore from '../containers/order-preference/store';
import buyInfoStore from '../containers/buy-info/store';
import shopInfoStore from '../containers/shop-info/store';
import goodsDetailStore from '../containers/goods-detail/store';
import goodsSkuStore from '../containers/goods-sku/store';
import goodsBottomStore from '../containers/goods-bottom/store';
import goodsTradeCarousel from '../containers/goods-trade-carousel/store';
import packageBuyStore from '../containers/discount-packages/store';

const stores = [
  activityBannerStore,
  goodsInfoStore,
  recommendStore,
  orderPreferenceStore,
  buyInfoStore,
  shopInfoStore,
  goodsDetailStore,
  goodsSkuStore,
  goodsBottomStore,
  packageBuyStore,
  goodsTradeCarousel,
];

export default stores.reduce((a, b) => mergeStore(a, b), {});
