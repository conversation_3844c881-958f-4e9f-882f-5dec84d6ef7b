<van-cell-group
  border="{{ false }}"
  class="shop-info__container"
>
  <van-cell value-class="cell-value__class">
    <view bindtap="navigateToHome" class="base-info__container">
      <view class="base-info__image">
        <image src="{{ shopLogo }}" />
      </view>
      <view class="base-info__title">
        <view class="base-info__title-name zan-ellipsis">{{ shopName }}</view>
        <block wx:key="{{ item }}" wx:for="{{ shopCertTags }}">
          <van-tag
            class="base-info__tag"
            custom-class="fit-tag-align"
            type="{{ tagColorMap[item] }}"
          >{{ item }}</van-tag>
        </block>
        <view
          style="margin-top: 5px;"
          class="zan-font-12 zan-c-gray-darker">
          {{ multiStoreDistance || multiStoreAddress }}
        </view>
      </view>
      <view bindtap="navigateToHome" class="base-info__tag">
        <van-tag
          plain
          color="{{ themeGeneral }}"
          custom-class="goods-detail__tag--plain"
        >进店</van-tag>
      </view>
    </view>
  </van-cell>
  <van-cell
    wx:if="{{ shopCertList.length }}"
    border="{{ showMultiStoreInfo }}"
    value-class="cell-value__class"
  >
    <view bind:tap="showPopup" class="cert-info__view">
      <view class="cert-info__item" wx:key="{{ index }}" wx:for="{{ shopCertList }}">
        <van-icon style="top:1px;position:relative;" name="passed" />
        {{ item.tag }}
      </view>
    </view>
  </van-cell>
  <van-cell
    is-link
    border="{{ false }}"
    title="{{ multiStoreLinkName }}"
    wx:if="{{ showMultiStoreInfo }}"
    value-class="cell-value__class"
    url="/packages/shop/multi-store/index/index?to=/packages/home/<USER>/index" />
</van-cell-group>

<goods-popup
  show="{{ isPopupShown }}"
  bind:tap:btn="closePopup">
  <van-cell-group border="{{ false }}" class="cert-info__popup">
    <van-cell
      wx:for="{{ shopCertList }}"
      wx:key="{{ index }}"
      border="{{ index !== shopCertList.length - 1 }}"
      value-class="cell-value__class"
      class="cert-info__item" >
      <van-icon name="passed" style="color: #44BB00;margin-left:1px;" />
      {{ item.name === '担保交易' ? '担保交易模式' : item.tag }}
      <view class="van-font-12 van-c-gray-darker" style="margin: 5px 0 0 18px">{{item.desc}}</view>
    </van-cell>
  </van-cell-group>
</goods-popup >