
.shop-info__container {
  margin-top: 10px;
  display: block;
}

.base-info {
  &__container {
    display: flex
  }

  &__image {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    
    image {
      width: 100%;
      height: 100%;
      border-radius: 2px;
    }
  }

  &__title {
    flex: 1;
    
    .officail__tag {
      font-size: 14px;
      border-radius: 2px;
      margin-top: 5px;
      display: inline-block;
    }
  }

  &__title-name {
    display: inline-block;
  }

  &__tag {
    display: inline-block;
    align-self: center;
    margin-left: 6px;
    width: 40px;
    font-size: 10px;
    white-space: nowrap;
  }
}

.goods-detail__tag--plain {
  width: 40px;
  padding: 3px 0 !important;
}

.fit-tag-align {
  line-height: 13px !important;
}

.cert-info {
  &__item {
    display: inline-block;
    font-size: 12px;
    color: #666;
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.cert-info__popup .cert-info__item {
  font-size: 12px;
  color: #111;
  display: block;
}

.goods-detail__tag--plain {
  box-sizing: border-box;
  text-align: center;
  padding: 4px 6px;
  width: 40px;
  justify-content: center;
}
.goods-detail__tag--plain::after {
  border-radius: 48px !important;
}