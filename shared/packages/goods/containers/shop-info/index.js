/**
 * 店铺信息
 */
import navigate from 'shared/utils/navigate';
import { mapGetters, mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';
import { tryLocation } from '@/helpers/lbs';
import { formatDistance } from '../../common';
import { calcDistance } from 'shared/utils/lbs/index';

VanxComponent({
  data: {
    themeGeneral: '',
    isPopupShown: false,
    shopLogo: '',
    tagColorMap: {
      官方: 'danger',
      公益: 'success'
    },
  },

  mapData: {
    // ?? 需要仔细测一下
    ...mapGetters([
      // 店铺基本信息
      'shopName',
      'shopLogo',
      // 官方、企业、个人认证
      'shopCertTags',
      // 门店认证信息
      'shopCertList',
      // 多店铺相关
      'showMultiStoreInfo',
      'multiStoreLinkName',
      'multiStoreAddress',
    ])
  },

  computed: {
    ...mapState({
      hideStore: (state) => state.multistore.hideStore
    }),
    ...mapState('/', {
      userInfo: state => state.userInfo,
      shop: state => state.shop
    })
  },

  watch: {
    hideStore(hide) {
      !hide && this.location();
    }
  },

  methods: {
    location() {
      if (!this.shop.isMultiStore) return;

      const location = this.userInfo.poi && this.userInfo.poi.location;
      const locationP = location ? Promise.resolve(location) : new Promise(tryLocation);

      locationP.then(({ lat, lng }) => {
        const { lat: latd, lng: lngd } = this.shop.store;
        const distance = calcDistance({ lat, lng }, { lng: lngd, lat: latd }) * 1000;
        if (distance) {
          let distanceStr = formatDistance(distance);

          if (this.data.multiStoreAddress) {
            distanceStr += this.data.multiStoreAddress;
          }

          this.setYZData({ multiStoreDistance: distanceStr });
        }
      });
    },
    navigateToHome() {
      navigate.switchTab({ url: '/packages/home/<USER>/index' });
    },

    showPopup() {
      this.setYZData({
        isPopupShown: true,
      });
    },

    closePopup() {
      this.setYZData({
        isPopupShown: false,
      });
    }
  },

  ready() {
    !this.hideStore && this.location();

    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  },
});
