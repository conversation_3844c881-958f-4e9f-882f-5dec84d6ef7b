import config from '../../../common/config';

export default {
  // 店铺tag ?? 原来的是只有官方和非官方
  // ?? 注释与H5代码不符合；店铺认证类型 2：企业认证 3-4：个人认证 5-9：官方认证
  shopCertTags(state) {
    const { certType } = state.shop;
    if (certType === 10) {
      return ['公益'];
    }
    if (certType >= 6) {
      return ['官方'];
    }
    return [];
  },

  // 门店认证信息
  shopCertList(state) {
    const {
      env: { isNewHopeKdt },
      refund: { isSupport },
      shop: { certType },
      shopConfig: {
        hasPhysicalStore, isSecuredTransactions, supportFreightInsurance, isWeixinPayOrigin
      }
    } = state;
    const shopCertList = [];

    // 店铺认证
    if (certType) {
      const shopCertConfig = config.TEAM_CERT[certType];
      if (shopCertConfig) {
        shopCertList.push({
          tag: shopCertConfig.tag,
          desc: config.CERT_DESC[shopCertConfig.type]
        });
      }
    }

    // 是否是微信自有商家
    if (isWeixinPayOrigin) {
      shopCertList.push({
        tag: '货款直接给商家',
        desc: config.CERT_DESC.weixin_pay_origin_money,
      });

      shopCertList.push({
        tag: '店铺负责发货&售后',
        desc: config.CERT_DESC.weixin_pay_origin_aftersale,
      });
    }

    // 是否加入担保交易
    if (isSecuredTransactions) {
      shopCertList.push({
        tag: '收货后结算',
        desc: config.CERT_DESC.is_secured_transactions
      });
    }
    // 新希望店铺隐藏线下门店
    if (hasPhysicalStore && !isNewHopeKdt) {
      shopCertList.push({
        tag: '线下门店',
        desc: config.CERT_DESC.team_physical
      });
    }
    // 店铺运费险对于分销、礼品卡、酒店、周期购、会员卡、虚拟商品无效
    if (supportFreightInsurance) {
      shopCertList.push({
        tag: '退货运费补贴',
        desc: config.CERT_DESC.freight_insurance
      });
    }
    // 虚拟商品是否支持退款
    if (!isSupport) {
      shopCertList.push({
        tag: '不支持申请退款',
        desc: config.CERT_DESC.virtual_unsupport_refund
      });
    }

    return shopCertList;
  },

  // 修改
  multiStoreLinkName(state, getters) {
    return state.multistore.id && !getters.showAddressContent ? '其他分店' : '线下门店';
  },

  // 店铺网店距离信息
  multiStoreAddress(state) {
    const { address } = state.multistore;

    return address;
  },

  multiStoreLink(state, getters) {
    const { id } = state.multistore;
    const { kdtId } = state.shop;
    const { h5 } = state.url;
    return id && !getters.showAddressContent
      ? `${h5}/wscump/multistore/index?kdt_id=${kdtId}`
      : `${h5}/wscump/physicalstore/index?kdt_id=${kdtId}`;
  },
};
