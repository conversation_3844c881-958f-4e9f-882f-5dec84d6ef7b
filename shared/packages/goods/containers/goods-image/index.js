import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import isEmpty from '@youzan/weapp-utils/lib/is-empty';
import getSystemInfo from 'shared/utils/browser/system-info';

VanxComponent({
  mapData: {
    ...mapState(['video']),
    ...mapState({
      formattedPictures(state) {
        const { picture = {}, video } = state;
        const { pictures = [] } = picture;
        let picsList = pictures;

        if (!picsList) {
          return;
        }

        if (picsList.length === 0) {
          picsList = ({}).pictures || [];
        }

        let pics = [...picsList];
        if (!isEmpty(video) && video.coverUrl) {
          pics.unshift({
            url: video.coverUrl,
            width: video.width,
            height: video.height
          });
        }
        this._previewIndex = 0;
        this._videoLoaded = false;

        const allPic = this.formatPictures(pics);
        return allPic;
      }
    })
  },

  // properties: {
  //   briefGoods: {
  //     type: Object,
  //     value: {}
  //   }
  // },

  data: {
    goods: {},
    width: 0,
    height: 0,
    previewIndex: 1,
    isVideoFullscreen: false
  },

  methods: {
    formatPictures(pics = []) {
      // 商品图片-格式化后的
      let pictureRatio = 0;
      const winWidth = getSystemInfo().windowWidth;
      let picture = pics.map(item => {
        pictureRatio = Math.min(
          1.5,
          Math.max(
            pictureRatio,
            Math.max(0.56, +item.height / +item.width)
          )
        );
        return cdnImage(item.url, '!730x0.jpg');
      });
      const picHeight = Math.ceil(winWidth * pictureRatio);

      this.setYZData({
        width: winWidth,
        height: picHeight,
        picHeight
      });

      return picture;
    },

    handleSwiperImageTap() {
      var images = this.data.formattedPictures || [];

      wx.previewImage({
        current: images[this._previewIndex],
        urls: images
      });
    },


    handleSwiperChange(e) {
      this._previewIndex = e.detail.current;
      this.setYZData({
        previewIndex: this._previewIndex + 1
      });
    },

    handleVideoPlayClicked() {
      if (!this.goodsVideo) {
        this.goodsVideo = wx.createVideoContext('goods-video-id', this);
      }

      this.setYZData({
        showVideo: true
      }, () => {
        this.goodsVideo.exitFullScreen();
        this.goodsVideo.play();
      });
    },

    /*
      小程序播放器无法监听原生组件点击事件，采用妥协方案
      1. 播放时隐藏关闭按钮
      2. 暂停时显示关闭按钮
      3. 播放结束隐藏播放器
    */
    clearVideoTimer(reset) {
      clearTimeout(this.videoControllTimer);
      // 5s后隐藏关闭按钮
      if (reset) {
        this.videoControllTimer = setTimeout(() => {
          this.setYZData({
            'mainVideo.showCloseVideoBtn': false
          });
        }, 5000);
      }
    },

    handleVideoPlay() {
      if (!this._videoLoaded) this._videoLoaded = true;
      if (this.data.isVideoFullscreen) return;
      this.setYZData({
        showCloseVideoBtn: false
      });
    },

    // 不知为啥加载时会触发pause事件
    handleVideoPause() {
      if (!this._videoLoaded) return;
      this.setYZData({
        showCloseVideoBtn: true
      });
    },

    handleVideoEnded() {
      this._videoLoaded = false;
      this.setYZData({
        showVideo: false,
        showCloseVideoBtn: false
      });
    },

    // 目前无法监听到原生视频内的点击
    handleVideoTap() {
    },

    handleVideoClose() {
      const goodsVideo = this.goodsVideo;
      if (!goodsVideo) return;

      // 重置视频
      goodsVideo.seek(0);
      goodsVideo.pause();
      // 执行播放结束流程
      this.handleVideoEnded();
    },

    handleFullscreenChange(e) {
      const fullScreen = e.detail.fullScreen;

      // 全屏时一直显示关闭按钮
      this.setYZData({
        isVideoFullscreen: fullScreen,
        showCloseVideoBtn: fullScreen
      });
    },

  }
});
