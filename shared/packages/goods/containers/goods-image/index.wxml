<view class="goods-image-panel">
  <view class="goods-image-tag">
    {{ previewIndex }}/{{ formattedPictures.length }}
  </view>
    <!-- swiper 高度要低于图片高度一点，有缝隙 -->
    <!-- 小程序模拟器没有自动轮播 ?? -->
  <swiper
    circular
    indicator-dots="{{ false }}" 
    autoplay="{{ formattedPictures.length > 1 }}" 
    style="width: {{ width }}px; height: {{ height - 2 }}px;" 
    bindtap="handleSwiperImageTap" 
    bindchange="handleSwiperChange"
  >
    <swiper-item wx:for="{{ formattedPictures }}" wx:key="unique">
      <image
        src="{{ item }}"
        mode="aspectFit"
        class="goods-image"
        data-index="{{ index }}"
        style="width: {{ width }}px; height: {{ height }}px;"
      />
      <!-- 播放按钮 -->
      <view
        wx:if="{{ index === 0 && video && video.coverUrl }}"
        class="goods-video-play"
        catch:tap="handleVideoPlayClicked"
      />
    </swiper-item>
  </swiper>

  <!-- 主图视频 -->
  <view wx:if="{{ video }}" hidden="{{ !showVideo }}" class="goods-image__video">
    <video
      id="goods-video-id" 
      class="goods-video"
      objectFit="contain"
      src="{{ video.videoUrl }}"
      style="height:{{ picHeight + 'px'}}"
      catch:tap="handleVideoTap"
      bindplay="handleVideoPlay" 
      bindpause="handleVideoPause" 
      bindended="handleVideoEnded" 
      bindfullscreenchange="handleFullscreenChange"
    >
      <cover-view
        hidden="{{ !showCloseVideoBtn }}"
        class="goods-video__close-btn {{ isVideoFullscreen ? 'goods-video__close-btn--fullscreen' : '' }}"
        bind:tap="handleVideoClose"
      >
        ×
      </cover-view>
    </video>
  </view>
</view>