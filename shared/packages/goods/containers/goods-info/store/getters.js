import get from '@youzan/weapp-utils/lib/get';
import { moment } from '@youzan/weapp-utils/lib/time';
import { splitPrice } from '../../../store/utils/helpers';

export default {
  // 本组件是否显示
  isGoodsInfoShow(state, getters) {
    return getters.saasGoodsInfoShow;
  },

  goodsTag(state, getters) {
    if (get(state, 'haitao.isHaitao')) {
      return '海淘';
    }
    if (getters.isPeriodbuy) {
      return '周期购';
    }
    return '';
  },
  // 商品标题
  goodsTitle(state, getters) {
    return getters.saasGoodsTitle || state.goods.title || '';
  },
  // 商品描述
  goodsSubTitle(state, getters) {
    if (getters.isPeriodbuy) {
      return getters.periodbuyActivity.description || '';
    }
    return getters.saasGoodsSubTitle || state.goods.sellPoint || '';
  },

  // 商品信息价格标签
  goodsPriceTag(state, getters) {
    const { saasGoodsPriceTag } = getters;
    if (saasGoodsPriceTag) {
      return saasGoodsPriceTag;
    }
    // 小程序里 限时折扣和会员折扣互斥
    const timelimited = get(state, 'orderActivity.timelimitedDiscount', null);
    // 限时折扣需要显示活动栏
    if (timelimited) {
      let showTag = '限时';
      showTag += timelimited.discountType === 2 ? timelimited.discountValue / 10 + '折' : '减价';
      return showTag;
    }
    const discount = get(state, 'orderActivity.discount' || {});
    return discount.title || '';
  },

  goodsOriginPriceTag(state) {
    const presale = state.goodsActivity.presale;
    if (!presale) {
      return '';
    }
    return '总价';
  },

  // 商品信息页上的价格标签
  goodsPrice(state, getters) {
    const { saasGoodsPrice } = getters;
    if (saasGoodsPrice) {
      return saasGoodsPrice;
    }

    const {
      isVirtualTicket,
      origin: originPrice
    } = state.goods || {};

    let {
      minPrice,
      maxPrice,
      price: currentPrice,
      oldPrice,
    } = getters.skuData || {};

    // 如果是定金预售，价格显示的是定金
    if (getters.isDepositPresale) {
      const presale = get(state, 'goodsActivity.presale', {});
      maxPrice = presale.maxDeposit;
      minPrice = presale.minDeposit;
    }

    const isRange = minPrice !== maxPrice;

    // 电子卡券加上价格日历特殊逻辑
    if (isVirtualTicket && state.priceDateRange) {
      return {
        currentPrice: state.priceDateRange
      };
    }

    return {
      isRange,
      currentPrice,
      minPriceData: splitPrice(minPrice),
      maxPriceData: splitPrice(maxPrice),
      originPrice: oldPrice || originPrice
    };
  },

  // 现在原价（划线价）显示有问题，H5和小程序不统一，先做如此处理，等待产品整理后做统一逻辑处理
  formattedOriginPrice(state, getters) {
    const {
      originPrice = ''
    } = getters.goodsPrice || {};
    if (!originPrice) {
      return '';
    }
    const result = `${originPrice}`.trim();
    // 如果原价全部由数字、-、空格组成，才自动加 ¥
    if (result && result !== '-' && result !== '.' && /^[0-9\- .]+$/.test(result)) {
      return `¥ ${result}`;
    }
    return result;
  },

  goodsTaxTag(state) {
    if (state.haitao && state.haitao.haiTaoItemExtra) {
      const { tariffAmount, tariffRule } = state.haitao.haiTaoItemExtra;
      if (tariffRule === 1) {
        // 0 不含税 1 含税， 含税不显示
        return '';
      }
      if (tariffAmount) {
        return `进口关税 预计 ${tariffAmount}`;
      }
    }
    return '';
  },

  virtualValidPeriodText(state) {
    // 卡券有效期类型 0：长期可用 1：xx天内可用 2：时间区间内可用
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const {
      validityType, validityDays, startDate, endDate
    } = virtualTicket;
    const startDateStr = moment(startDate, 'YYYY-MM-DD');
    const endDateStr = moment(endDate, 'YYYY-MM-DD');
    switch (validityType) {
      case 0:
        return '生效时间起长期可用';
      case 1:
        return `生效时间起${validityDays}天内可用`;
      case 2:
        return `在${startDateStr}至${endDateStr}内可用`;
      case 3:
        return '在所选使用日期内当天可用';
      default:
        return '';
    }
  },

  virtualEffectiveTime(state) {
    // 卡券生效类型 0：立即生效 1：xx小时后生效 2：次日生效
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const {
      effectType, effectDelayTime,
    } = virtualTicket;

    const returnText = '付款完成后,';
    switch (effectType) {
      case 0:
        return `${returnText}立即生效`;
      case 1:
        return `${returnText}${effectDelayTime}小时生效`;
      case 2:
        return `${returnText}次日生效`;
      case 3:
        return `${returnText}立即生效`;
      default:
        return '';
    }
  },

  virtualHolidaysText(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    if (typeof virtualTicket.isHolidaysAvailable === 'number') {
      return virtualTicket.isHolidaysAvailable ? '节假日可用' : '节假日不可用';
    }
    return '';
  },

  virtualRefundDocument(state) {
    const { isSupport, type, interval } = state.refund;
    if (!isSupport) {
      return '不支持申请退款';
    }
    if (type === 0) {
      return '未使用卡券随时可申请退款';
    }
    if (type === 1) {
      const day = Math.floor(interval / 24 / 60 / 60 / 1000);
      const left = interval % (24 * 60 * 60 * 1000);
      const hour = Math.floor(left / 60 / 60 / 1000);
      const dayStr = day ? `${day}天` : '';
      const hourStr = hour ? `${hour}小时` : '';
      return `未使用卡券在过期前${dayStr + hourStr}随时可以申请退款`;
    }
    return '';
  },

  // 库存展示
  showGoodsStock(state, getters) {
    const { sku, isHotel } = state;
    const { goodsStockNum } = getters;

    return !sku.hideStock && !isHotel && goodsStockNum > 0;
  },

  // 销量展示
  showSoldNum(state, getters) {
    // const { goods } = state;
    const goodsDetailSales = get(state, 'shopConfig.goodsDetailSales', null);
    const showBuyRecord = get(state, 'shopConfig.showBuyRecord', null);
    // const { soldNum = 0 } = goods;
    const soldNum = getters.goodsSoldNum;

    if (getters.isPeriodbuy) {
      return false;
    }

    // 如果没有新值就用老的销量字段值
    let parsedGoodsDetailSales = showBuyRecord
      ? { show: 1, limit: false }
      : { show: 0, limit: false };

    if (goodsDetailSales) {
      try {
        parsedGoodsDetailSales = JSON.parse(goodsDetailSales) || parsedGoodsDetailSales;
      } catch (error) {
        // do nothing
      }
    }

    if (!parsedGoodsDetailSales) {
      return soldNum > 0;
    }

    const { show = 0, limit = false, limit_num: limitNum = 0 } = parsedGoodsDetailSales;

    if (+show === 0) {
      return false;
    } if (!limit) {
      return soldNum > 0;
    }

    return soldNum >= limitNum;
  },

  // 运费展示
  showPostage(state, getters) {
    if (getters.isPeriodbuy) {
      return false;
    }

    const { distribution, isHotel } = state;
    const { isMultiStoreSoldout } = getters;

    return distribution.postage && !isHotel && !isMultiStoreSoldout;
  },
};
