import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import theme from 'shared/common/components/theme-view/theme';

const app = getApp();

VanxComponent({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  mapData: {
    ...mapState([
      'goods',
      // 运费相关逻辑
      'distribution',
      // 担保交易相关信息
      'guarantee',
    ]),
    ...mapState({
      isVirtualTicket: (state) => state.goods.isVirtualTicket,
    }),
    ...mapGetters([
      'isGoodsInfoShow',
      'goodsTag',
      'goodsTitle',
      'goodsPrice',
      'goodsSubTitle',
      // 价格标签
      // 会展示 n人可拼，会员折扣 等信息
      'goodsPriceTag',
      // 原价相关
      'goodsOriginPriceTag',
      // 海淘税费
      'goodsTaxTag',
      // 定金预售倒计时信息
      'countdown',
      // 定金预售商品可抵价格显示
      'offsetPriceRange',
      // 是否是定金预售
      'isDepositPresale',
      // 销量信息
      'showSoldNum',
      'goodsSoldNum',
      // 库存信息
      'showGoodsStock',
      'goodsStockNum',
      // 是否显示运费
      'showPostage',
      // 前缀处理过的划线价
      'formattedOriginPrice',
    ])
  },

  properties: {
    // 组件层面自定义，满足插件里的自定义需求
    showShare: {
      type: Boolean,
      value: true
    }
  },

  data: {
    themeGeneral: ''
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  },

  methods: {
    handleShareClick() {
      app.logger && app.logger.log({
        et: 'click',
        ei: 'goods_share',
        en: '点击分享按钮',
        si: app.getKdtId(), // 店铺id，没有则置空
        params: {},
      });
      this.triggerEvent('share');
    }
  }
});
