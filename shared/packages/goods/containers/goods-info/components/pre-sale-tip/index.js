import { VanxComponent } from 'shared/common/base/wsc-component/index';
import get from '@youzan/weapp-utils/lib/get';
import { moment } from '@youzan/weapp-utils/lib/time';
import { mapState, mapGetters } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState({
      startTime: (state) => moment(get(state, 'goodsActivity.presale.payStartTime', 0), 'YYYY.MM.DD HH:mm:ss'),
      endTime: (state) => moment(get(state, 'goodsActivity.presale.payEndTime', 0), 'YYYY.MM.DD HH:mm:ss'),
    }),
    ...mapGetters([
      // 尾款价格
      'tailPrice',
      'isHelpDepositPresale'
    ]),
  },
  properties: {
    themeClass: String
  },

  methods: {
    showPopup() {
      this.setYZData({
        show: true
      }, {
        immediate: true
      });
    },

    closePopup() {
      this.setYZData({
        show: false
      }, {
        immediate: true
      });
    }
  },
});
