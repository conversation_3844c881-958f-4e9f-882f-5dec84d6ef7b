  <goods-tip-view
    is-link="{{ !!instructions }}"
    title="电子卡券"
    class="virtual-ticket-info"
    link-tip="{{ instructions ? '使用说明' : '' }}"
    bind:tap:cell="showPopup"
  >
    <view>有效期：{{ virtualValidPeriodText }}</view>
    <view>生效时间：{{ virtualEffectiveTime }}</view>
    <view wx:if="{{ virtualHolidaysText }}">节假日：{{ virtualHolidaysText }}</view>
    <view wx:if="{{ virtualRefundDocument }}">售后服务：{{ virtualRefundDocument }}</view>
  </goods-tip-view>

  <goods-popup
    show="{{ show }}"
    title="使用说明"
    bind:tap:btn="closePopup"
  >
    <view class="e-card__instr-text">
      {{  instructions }}
    </view>
  </goods-popup>