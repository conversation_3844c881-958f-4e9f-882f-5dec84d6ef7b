import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import get from '@youzan/weapp-utils/lib/get';

VanxComponent({
  properties: {
    themeClass: String,
  },

  data: {
    show: false,
  },

  mapData: {
    ...mapState({
      instructions: (state) => get(state, 'goodsActivity.virtualTicket.instructions', ''),
    }),
    ...mapGetters([
      'virtualValidPeriodText',
      'virtualEffectiveTime',
      'virtualHolidaysText',
      'virtualRefundDocument',
    ]),
  },

  methods: {
    showPopup() {
      if (!this.data.instructions) {
        return;
      }

      this.setYZData({
        show: true
      });
    },

    closePopup() {
      this.setYZData({
        show: false
      });
    }
  }
});
