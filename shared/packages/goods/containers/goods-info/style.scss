@import '~shared/common/css/_mixins.scss';

.goods-base {
  &__container {
    background: #fff;
    overflow: hidden;
  }

  &__cell {
    padding-top: 10px !important;
  }

  &__header {
    display: flex;
    margin-bottom: 5px;
    
    .share-image__icon {
      width: 20px;
      height: 20px;
      margin-left: 15px;
      font-size: 10px;
      color: #666;

      & .share-text {
        white-space: nowrap;
        line-height: 10px;
      }
    }

    .share-icon {
      font-size: 20px;
    }
  }

  &__title {
    flex: 1;
    
    .goods-title {
      margin-bottom: 5px;
      min-height: 16px;
      word-break: break-all;
      color: #111;
      font-size: 16px;
      font-weight: bold;

      &__tag {
        margin: -1px 4px 0 0;
        vertical-align: middle;
        font-weight: normal;
      }
    }

    .goods-desc {
      word-break: break-all;
    }
  }
}

.goods-price__container {
  .price-desc {
    display: flex;
    font-weight: bolder;
    align-items: baseline;
  }

  .price-origin {
    margin-top: 5px;
    line-height: 12px;
  }

  .price-value {
    white-space: nowrap;
    display: flex;
    align-items: baseline;
    line-height: 24px;
    min-height: 24px;
    position: relative;
    top: 1px;
  }

  .price-activity__tag {
    border-radius: 23px 0 0 23px;
    height: 16px;
    font-size: 12px;
    color: #ffffff;
    line-height: 16px;
    text-align: right;
    margin-right: 10px;
    padding-left: 5px;
    padding-right: 2px;
    transform: translateY(-2px);
    position: relative;
  }

  .customer-discount__tag {
    color: #f44;
    font-size: 10px;
    padding: 1px 2px;
    margin-left: 5px;
    text-align: center;
    position: relative;
    line-height: 16px;
    height: 16px;
    top: -2px;
    box-sizing: border-box;

    &::after {
      @include border-all(#f44);
      border-radius: 4px;
    }
  }
}

.line-height--18 {
  line-height: 18px;
}

.stock-critical {
  color: red;
}

.goods-info__cell,
.youzan-security__cell {
  padding: 10px 15px;
}

.youzan-security__cell {
  height:36px;
  box-sizing:border-box;
}

.goods-info__container {
  display: flex;

  text {
    flex: 1;
    color: #666;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;

    &:first-child {
      text-align: left;
    }

    &:last-child {
      text-align: right;
    }
  }
}

.goods-info__text {
  flex: 1;
  color: #666;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  text-align: left;
}

.youzan-security__cell {
  padding: 0 15px !important;
}