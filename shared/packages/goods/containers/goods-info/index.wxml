<template name="goods-price">
  <view wx:if="{{ yuan }}" class="van-font-14" style="margin-right: 3px;">¥</view>
  <block wx:if="{{ yuan && fen }}">
    <text class="van-font-20" style="line-height: 20px;">{{ yuan }}</text>
    <text wx:if="{{ fen }}" class="van-font-14">.{{ fen }}</text>
  </block>
  <text wx:if="{{ yuan && points }}" style="margin: 0 5px"> + </text>
  <text wx:if="{{ points }}" class="van-font-14"><text class="van-font-18">{{ points }}</text> {{ pointsName }}</text>
</template>

<view wx:if="{{ isGoodsInfoShow }}" class="goods-base__container">
  <van-cell-group border="{{ false }}">
    <van-cell value-class="cell-value__class" custom-class="goods-base__cell">
      <view class="goods-base__header">
        <!-- 商品标题区域 -->
        <view class="goods-base__title">
          <view class="goods-title">
            <van-tag
              wx:if="{{ goodsTag }}"
              type="danger"
              color="{{ themeGeneral }}"
              custom-class="goods-title__tag"
            >{{ goodsTag }}</van-tag>
            {{ goodsTitle }}
          </view>
          <view class="van-multi-ellipsis--l2 van-font-12 van-c-gray-darker goods-desc">
            {{ goodsSubTitle }}
          </view>
        </view>
        <!-- 分享按钮 -->
        <form-view
          wx:if="{{ showShare }}"
          style="width: 20px; display: block;"
          class="share-image__icon"
        >
          <theme-view color="general">
            <yz-icon bindtap="handleShareClick" custom-class="share-icon" name="icon-goods-share"></yz-icon>
          </theme-view>
          <view class="share-text" bindtap="handleShareClick">分享</view>
        </form-view>
      </view>
      <view class="goods-price__container">
        <view class="price-desc">
          <!-- 价格前面可能有 label -->
          <!-- <view wx:if="{{ goods.price.label }}" style="margin-right: 3px;">{{ goods.price.label }}</view> -->
          <!-- 支持价格日历的卡券 并且价格存在 -->
          <theme-view
            color="general"
            custom-class="price-value van-font-bold"
          >
            <view
              wx:if="{{ isDepositPresale }}"
              class="van-font-12"
            >定金 </view>
            <template is="goods-price" data="{{ ...goodsPrice.minPriceData }}"></template>
            <block wx:if="{{ goodsPrice.isRange }}">
              <view style="margin: 0 5px;">-</view>
              <template is="goods-price" data="{{ ...goodsPrice.maxPriceData }}"></template>
            </block>
          </theme-view>

          <van-tag
            wx:if="{{ goodsPriceTag }}"
            plain
            type="danger"
            class="line-height--18"
            color="{{ themeGeneral }}"
            custom-class="goods-detail__tag--plain"
          >{{ goodsPriceTag }}</van-tag>
          <van-tag
            wx:if="{{ offsetPriceRange }}"
            type="danger"
            class="line-height--18"
            color="{{ themeGeneral }}"
            custom-class="goods-detail__tag--plain"
            plain
          >{{ offsetPriceRange }}</van-tag>
          <!-- goods-info-price-tag 通过插槽给外部使用 -->
          <slot name="goods-info-price-tag" />
        </view>
        <!-- 原价显示 -->
        <!-- 混入定金预售逻辑 -->
        <view
          wx:if="{{ isDepositPresale }}"
          class="price-origin van-font-12 van-c-gray-darker"
        >总价 {{ goodsPrice.originPrice }}</view>
        <view
          wx:elif="{{ formattedOriginPrice }}"
          class="price-origin van-font-12 van-c-gray-darker"
        >
          <text
            class="van-text-deleted"
          >{{ formattedOriginPrice }}</text>
        </view>
      </view>
    </van-cell>
    <van-cell wx:if="{{ goodsTaxTag }}" cell-class="goods-info__cell">
      <view class="goods-info__text">
        {{ goodsTaxTag }}
      </view>
    </van-cell>
    <van-cell wx:if="{{ showPostage }}" border="{{ !goods.hasActivityDesc && guarantee.on }}" cell-class="goods-info__cell">
      <view class="goods-info__container">
        <text>运费 {{ distribution.postage }}</text>
        <text>{{ showSoldNum ? ("销量 " + goodsSoldNum) : " "}}</text>
        <text
          wx:if="{{ showGoodsStock }}"
          class="{{ goodsStockNum < 50 ? 'stock-critical' : '' }}"
        >剩余 {{ goodsStockNum }}</text>
      </view>
    </van-cell>

    <!-- 定金预售 -->
    <pre-sale-tip
      wx:if="{{ isDepositPresale }}"
    />

    <!-- goods-info-tip 通过插槽给外部使用 -->
    <slot name="goods-info-tip" />

    <!-- 电子卡券 -->
    <e-card-tip
      wx:if="{{ isVirtualTicket }}"
    />

    <van-cell
      wx:if="{{ guarantee.on }}"
      border="{{ false }}"
      value-class="cell-value__class"
      cell-class="youzan-security__cell"
      custom-class="youzan-security__cell"
    >
      <youzan-security style-type="{{ guarantee.style }}" />
    </van-cell>

    <!-- 分期支付 -->
    <instalment-payment wx:if="{{ goods.isInstallment }}" />
  </van-cell-group>

</view>
