/**
 * 打包一口价
 */
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import { mapState } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { formatPrice } from '../../common/index';

VanxComponent({

  mapData: {
    ...mapState({
      bale: state => state.orderActivity.bale || {},
      balePrice: state => formatPrice(get(state, 'orderActivity.bale.price', 0)),
      showBale: state => !!state.orderActivity.bale,
    })
  },

  methods: {
    handleCellClick() {
      const { bale = {} } = this.data;
      const {
        id,
        price,
        num,
        alias,
      } = bale;
      wx.navigateTo({
        url: args.add('/packages/ump/bundle-purchase/goods-list/index', {
          activityId: id,
          activityAlias: alias,
          totalPrice: price,
          requiredCount: num,
        })
      });
    }
  }
});
