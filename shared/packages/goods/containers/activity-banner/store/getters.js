export default {

  // 普通活动-如果是预售需要显示activity-banner
  showActivityBanner(state, getters) {
    const { orderActivity = {} } = state;
    // 限时折扣需要显示活动栏
    if (orderActivity.timelimitedDiscount) {
      return true;
    }
    // 定金预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      return true;
    }
    return false;
  },

  // 活动标题
  activityTitle(state, getters) {
    const { orderActivity = {} } = state;
    // 限时折扣需要显示活动栏
    if (orderActivity.timelimitedDiscount) {
      return orderActivity.timelimitedDiscount.title || '限时折扣';
    }

    if (getters.isHelpDepositPresale) {
      return '助力定金膨胀';
    }

    if (getters.isDepositPresale) {
      return '预售';
    }
    const discount = orderActivity.discount || {};
    return discount.title || '';
  },
};
