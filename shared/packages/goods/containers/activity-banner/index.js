import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { countDownRemainParse } from '@youzan/weapp-utils/lib/time';
import { mapGetters } from '@youzan/vanx';

// import { ACTIVITY_TYPE, ACTIVITY_STATUS, GOODS_TYPE_MAP } from '../../common/constant';

// const supportActivity = {
//   SECKILL: ACTIVITY_TYPE.SECKILL,
//   PACK_PRICE: ACTIVITY_TYPE.PACK_PRICE,
//   GROUP_BUY: ACTIVITY_TYPE.GROUP_BUY,
//   DISCOUNT: ACTIVITY_TYPE.DISCOUNT,
//   GROUP_ON: ACTIVITY_TYPE.GROUP_ON,
//   PRICE_DROP: ACTIVITY_TYPE.PRICE_DROP,
//   CROWDFUMNDING: ACTIVITY_TYPE.CROWDFUMNDING,
//   ENTITY_PRE_SALE: ACTIVITY_TYPE.ENTITY_PRE_SALE,
//   HELPCUT: ACTIVITY_TYPE.HELPCUT,
//   GROUP_ON_DRAW: ACTIVITY_TYPE.GROUP_ON_DRAW
// };

// const ONE_DAY = 24 * 60 * 60 * 1000;

VanxComponent({
  mapData: {
    ...mapGetters([
      'showActivityBanner',
      'activityTitle',
      'countdown',
    ])
  },

  data: {
    countData: [],
  },

  watch: {
    // 如果countdown 变化了，需要重置一下，然后重新渲染倒计时
    'data.countdown': function () {
      this.fromStartToEnd = false;
      this.runCountDown();
    },
  },

  ready() {
    // 标记是否经历过从开始到结束的过程，如果结束则刷新页面
    this.fromStartToEnd = false;
    this.runCountDown();
  },

  detached() {
    clearTimeout(this._timer);
  },

  methods: {
    resetCountData() {
      if (this.data.countData && this.data.countData.length > 0) {
        this.setYZData({
          countData: [],
        });
      }
    },

    runCountDown() {
      clearTimeout(this._timer);
      const { countdown = {} } = this.data;
      if (!countdown.end) {
        // 没有结束时间，则重置为不显示
        this.resetCountData();
        return;
      }
      const remain = countdown.end - Date.now();

      if (remain <= 0) {
        if (this.fromStartToEnd) {
          setTimeout(() => {
            wx.startPullDownRefresh();
          }, 500);
        } else {
          // 说明一进入倒计时就结束
          this.resetCountData();
        }
        return;
      }

      const countData = countDownRemainParse(remain);
      // 说明倒计时启动了
      this.fromStartToEnd = true;
      this.setYZData({
        countData,
      });
      clearTimeout(this._timer);
      this._timer = setTimeout(() => {
        this.runCountDown();
      }, 1000);
    },
  }
});
