<theme-view
  gradient
  gradient-deg="45"
  color="main-text"
  wx:if="{{ showActivityBanner }}"
  custom-class="count-down__container"
>
  <view class="count-down__desc">{{ activityTitle }}</view>
  <view class="count-down__date">
    <view class="countdown__label">{{ countdown.desc }}</view>
    <view class="count-down__timer">
      <block wx:for="{{ countData }}" wx:key="{{ index }}">
        <view class="countdown--num">{{ item.value }}</view>
        <view class="countdown--split">{{ item.unit }}</view>
      </block>
    </view>
  </view>
</theme-view>