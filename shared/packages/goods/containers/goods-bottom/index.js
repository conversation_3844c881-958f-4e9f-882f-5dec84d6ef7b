/**
 * 底部按钮区域，用于放置客服、购物车等按钮
 */

import get from '@youzan/weapp-utils/lib/get';
import navigate from 'shared/utils/navigate';
import { isNewIphone } from 'shared/utils/browser/device-type';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';
import { mapGetters, mapState, mapActions } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

const app = getApp();
const p = 'packages';
// 位于 slot 中的自定义组件没有触发 pageLifetimes 中声明的页面生命周期，此问题在 2.5.2 中修复。
VanxComponent({
  properties: {
    useEvent: Boolean
  },

  data: {
    deviceType: isNewIphone() ? 'iPhone-X' : '',
    loading: false
  },

  mapData: {
    ...mapState(['goods', 'cartInfo']),
    ...mapGetters([
      'isGoodsBottomShow',
      'skuData',
      'shopRisk',
      'goodsBottomView',
      'goodsBottomTip',
      'homeBtnView',
      'miniCartBtnView',
      'imBtnView',
      'giftBtnView',
      'bookEvent',
      'imPath',
      'viceGradient',
      'mainGradient',
      'buttomImButtonText'
    ]),
  },

  methods: {
    ...mapActions(['showSkuPop', 'fetchCartCount', 'makeReservation', 'startCut']),

    handleBack() {
      navigate.switchTab({ url: `/${p}/home/<USER>/index` });
    },

    // 客户咨询按钮，埋点
    handleMessageBtnClick() {
      const { goods = {}, skuData = {} } = this.data || {};

      const params = {
        goods_id: goods.id,
        goods_name: goods.title,
        goods_alias: goods.alias || '',
        picture_url: goods.picture,
        price: skuData.minPrice / 100,
        show_price: skuData.price,
        group_ids: goods.group
      };
      app.logger
        && app.logger.log({
          et: 'click',
          ei: 'consult',
          en: '咨询',
          params,
          si: app.getKdtId()
        });
    },

    // 小购物车按钮，跳转页面
    handleMiniCartBtnClick() {
      const url = get(this.data, 'miniCartBtnView.url', `/${p}/goods/cart/index`);
      navigate.switchTab({ url });
    },

    handleHomeBtnClick() {
      const url = get(this.data, 'homeBtnView.url', `/${p}/home/<USER>/index`);
      navigate.switchTab({ url });
    },

    handleBtnClick(e) {
      let dataset = e.currentTarget.dataset;
      const btnType = dataset.btnType || 'main';
      let btnData = this.data.goodsBottomView[btnType] || {};

      if (btnData.disabled) {
        return;
      }

      if (btnData.path) {
        if (btnData.switchTab) {
          return navigate.switchTab({ url: btnData.path });
        }
        if (btnData.navigate) {
          return navigate.navigate({ url: btnData.path });
        }
        if (btnData.redirect) {
          return navigate.redirect({ url: btnData.path });
        }
      }

      // 触发分享
      if (btnData.skuScene === SKU_SCENE.SHARE) {
        this.triggerEvent('share');
        return;
      }

      // 是否是预约，比如秒杀预约
      if (btnData.skuScene === SKU_SCENE.RESERVATION) {
        // 提交vanx dispatch预约
        this.makeReservation();
        return;
      }

      // 开始砍价
      if (btnData.skuScene === SKU_SCENE.START_CUT) {
        this.startCut();
        return;
      }

      // 送礼商品
      if (dataset.isGift) {
        btnData = {
          ...btnData,
          skuScene: SKU_SCENE.GIFT_BUY,
          accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
        };
      }

      // 点击底部购买或加入购物车按钮
      // 表示点击了mainBtn或viceBtn
      // 把对应的scene等信息传递过去
      this.showSkuPop(btnData);
    },

    // 客服回调
    onContactBack: navigate.contactBack,
  },

  ready() {
    this.fetchCartCount();
  },
});
