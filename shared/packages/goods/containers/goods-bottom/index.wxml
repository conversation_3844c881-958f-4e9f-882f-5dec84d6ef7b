<form-view wx:if="{{ isGoodsBottomShow }}" class="goods-buy__container {{ deviceType }}">
  <van-notice-bar
    wx:if="{{ shopRisk.match }}"
    text="{{ shopRisk.note }}"
    backgroundColor="#FDE6E9"
    color="#323233"
  />
  <block wx:if="{{ !goodsBottomView.statusBar.text }}">
    <goods-bottom-tip
      wx:if="{{ goodsBottomTip }}"
      text="{{ goodsBottomTip }}"
    />
    <goods-bottom-count-down
      wx:if="{{ goods.waitToSoldTime  }}"
      sold-time="{{ goods.waitToSoldTime }}"
    />
  </block>
  <block wx:if="{{ goodsBottomView.statusBar.text && !goodsBottomView.statusBar.canBuy }}">
    <goods-bottom-tip>
      <navigator
        wx:if="{{ goodsBottomView.statusBar.link}}"
        url="{{ goodsBottomView.statusBar.link }}"
      >{{ goodsBottomView.statusBar.text }}</navigator>
      <block wx:else>{{ goodsBottomView.statusBar.text }}</block>
    </goods-bottom-tip>
    <view class="goods-buy__buttons">
      <view
        wx:if="{{ homeBtnView.show }}"
        class="icon-btn__view"
        bind:tap="handleHomeBtnClick"
      >
        <van-icon class="icon-btn__icon" name="home-o" style="margin-left: 0" />
        <view class="icon-btn__text">首页</view>
      </view>

        <theme-view
          bg="main-bg"
          color="main-text"
          style="flex: 1;"
          bindtap="handleBack"
          custom-class="goods-action-btn"
        >
          查看店铺其他商品
        </theme-view>
    </view>
  </block>
  <block wx:else>
    <goods-bottom-tip wx:if="{{ goodsBottomView.statusBar.text && goodsBottomView.statusBar.canBuy }}">
      {{ goodsBottomView.statusBar.text }}
    </goods-bottom-tip>
    <view class="goods-buy__buttons">
      <!-- 回到主页 -->
      <view
        wx:if="{{ homeBtnView.show }}"
        class="icon-btn__view"
        bind:tap="handleHomeBtnClick"
      >
        <van-icon class="icon-btn__icon" name="home-o" style="margin-left: 0" />
        <view class="icon-btn__text">首页</view>
      </view>

      <zan-contact
        wx:if="{{ imBtnView.show }}"
        custom-class="{{ message.size === 'md' ? 'icon-btn__view--md' : 'icon-btn__view' }}"
        bindtap="handleMessageBtnClick"
      >
        <van-icon class="icon-btn__icon" name="chat-o" style="margin-left: 0" />
        <view class="icon-btn__text">{{ buttomImButtonText }}</view>
      </zan-contact>

      <view wx:if="{{ giftBtnView.show }}" class="icon-btn__view" data-is-gift="{{ true }}" catch:tap="handleBtnClick">
        <van-icon class="icon-btn__icon" name="gift-o" style="margin-left: 0;" />
        <view class="icon-btn__text">送人</view>
      </view>

      <view wx:if="{{ miniCartBtnView.show }}"
        class="icon-btn__view {{ cartInfo.count ? 'shopping-cart--has-goods' : '' }} {{ miniCartBtnView.size === 'md' ? 'icon-btn__view--md' : '' }}"
        bind:tap="handleMiniCartBtnClick">
        <van-icon class="icon-btn__icon" name="cart-o" style="margin-left: 0" />
        <view class="cart-icon__count">{{ cartInfo.count }}</view>
        <view class="icon-btn__text">购物车</view>
      </view>

      <button wx:if="{{ goods.limitBuy }}" class="buy-btn zan-btn--large disabled-btn">仅限特定会员购买</button>
      <block wx:else>
        <user-authorize
          wx:if="{{ goodsBottomView.vice.show }}"
          class="goods-action-btn"
          bind:next="handleBtnClick"
          scene="{{ goodsBottomView.vice.accountUnionScene }}"
          data-btn-type="vice"
          class="goods-action-btn"
        >
          <theme-view
            bg="vice-bg"
            color="vice-text"
            gradient-start="{{ viceGradient.start }}"
            gradient-end="{{ viceGradient.end }}"
            gradient-deg="{{ viceGradient.deg }}"
            gradient="{{ viceGradient.be }}"
          >
            <button
              class="goods-action-btn {{ goodsBottomView.vice.disabled ? 'disabled-btn' : 'use-theme-color'}}"
              disabled="{{ goodsBottomView.vice.disabled }}"
            >
              <view class="goods-action__content">
                <view class="goods-action__desc {{ goodsBottomView.vice.sub && 'has-sub'}}">
                  <view class="one-line main-text">{{ goodsBottomView.vice.text }}</view>
                  <view class="van-font-12" wx:if="{{ goodsBottomView.vice.sub }}">{{ goodsBottomView.vice.sub }}</view>
                </view>
              </view>
            </button>
          </theme-view>
        </user-authorize>
        <subscribe-message
          wx:if="{{ goodsBottomView.main.show }}"
          bind:next="handleBtnClick"
          scene="{{ goodsBottomView.main.accountUnionScene }}"
          subscribe-scene="{{ goodsBottomView.main.subscribeScene }}"
          class="goods-action-btn"
          data-btn-type="main"
        >
          <theme-view
            bg="main-bg"
            color="main-text"
            gradient-start="{{ mainGradient.start }}"
            gradient-end="{{ mainGradient.end }}"
            gradient-deg="{{ mainGradient.deg }}"
            gradient="{{ mainGradient.be }}"
          >
            <button
              class="goods-action-btn goods-action__main-btn {{ goodsBottomView.main.disabled ? 'disabled-btn' : 'use-theme-color'}}"
              disabled="{{ goodsBottomView.main.disabled }}"
            >
              <view class="goods-action__content {{ goodsBottomView.main.hasLongText && 'has-long-text'}}">
                <view wx:if="{{ goodsBottomView.main.left }}" class="goods-action__desc normal align-left {{ goodsBottomView.main.left.sub && 'has-sub' }}">
                  <text class="van-font-12 main-text {{ goodsBottomView.main.left.main.class }}" wx:if="{{ goodsBottomView.main.left.main }}">
                    <block wx:if="{{ goodsBottomView.main.left.main.text }}">{{ goodsBottomView.main.left.main.text }}</block>
                    <block wx:else>{{ goodsBottomView.main.left.main }}</block>
                  </text>
                  <text class="van-font-12" wx:if="{{ goodsBottomView.main.left.sub }}">{{ goodsBottomView.main.left.sub }}</text>
                </view>
                <view class="goods-action__desc {{ goodsBottomView.main.sub && 'has-sub'}}">
                  <view class="one-line main-text">{{ goodsBottomView.main.text }}</view>
                  <view class="van-font-12" wx:if="{{ goodsBottomView.main.sub }}">{{ goodsBottomView.main.sub }}</view>
                </view>
              </view>
            </button>
          </theme-view>
        </subscribe-message>
      </block>
    </view>
  </block>
</form-view>
