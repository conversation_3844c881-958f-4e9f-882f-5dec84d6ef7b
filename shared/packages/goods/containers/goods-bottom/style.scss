@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.right-line {
  border-right: 1rpx solid #e5e5e5
}

.goods-buy {
  &__container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    box-shadow: 0 0 2px 0 rgba($color: #000000, $alpha: 0.1);
    background: #fff;

    &.iPhone-X::after {
      @include iphone-x-after($iphone-x-bottom);
    }
  }

  // &__tips {
  //   background: rgba(255, 247, 204, 0.95);
  //   color: #f44;
  //   font-size: 12px;
  //   height: 35px;
  //   line-height: 35px;
  //   text-align: center;
  // }

  &__buttons {
    display: flex;
    align-items: center;

    .icon-btn {
      &__view, &__view--md {
        background: #fff;
        text-align: center;
        width: 50px;
        height: 50px;
        overflow: hidden;
        position: relative;

        button {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          opacity: 0;
        }
      }
      
      &__view--md {
        width: 70px;
      }

      
      &__icon {
        position: relative;
        font-size: 20px;
        line-height: 24px;
        color: #666;
        padding-top: 8px;
        display: block;
      }

      &__text {
        font-size: 10px;
        color: #666;
        line-height: 16px;
      }
    }

    .buy-btn {
      flex: 1;
      display: flex;
      justify-content: center;
      padding-left: 0;
      padding-right: 0;
    }
    
    .goods-action {
      &-btn {
        height: 50px;
        line-height: 50px;
        border: 0;
        border-radius: 0 !important;
        padding: 0;
        position: relative;
        font-size: 16px;
        box-sizing: border-box;
        text-decoration: none;
        text-align: center;
        vertical-align: middle;
        overflow: visible;
        flex: 1;
        width: 100%;

        .btn-loading__mask {
          display: none;
        }
        
        &::after {
          content: none
        }

        &.zan-btn--loading {
          pointer-events: none;

          .btn-loading__mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            font-size: 14px;
            align-items: center;
            display: flex;
            z-index: 2;
          }

          .goods-action__content {
            display: none;
          }
        }
      }

      &__content {
        display: flex;
        align-items: center;
        & > view {
          height: 50px;
          flex: 1;
        }
      }
      &__desc {
        display: flex;
        justify-content: center;
        flex-direction: column;
        padding: 9px 10px;
        box-sizing: border-box;
      }
      
      &__desc.has-sub {
        line-height: 20px;
      }

      &__desc > text {
        flex: 1;
        white-space: nowrap;
      }

      @media (max-width: 320px) {
        &__content.has-long-text {
          font-size: 12px;
        }

        .has-long-text &__desc>text.van-font-12 {
          font-size: 8px;
        }
      }

      &-auth-btn {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 2;
        left: 0;
        top: 0;
      }
    }
  }
}

.one-line {
  white-space: nowrap;
}

.main-text {
  font-weight: bolder;
  font-size: 14px;

  &.normal {
    font-size: 12px;
    font-weight: normal;
  }
}

.align-left {
  text-align: left;
}

.cart-icon__count {
  display: none;
}

.shopping-cart--has-goods .cart-icon__count {
  top: 3px;
  z-index: 2;
  right: 7px;
  padding: 2px;
  display: block;
  position: absolute;
  color: #fff;
  font-size: 8px;
  text-align: center;
  background: #f44;
  line-height: 9px;
  height: 14px;
  min-width: 14px;
  border-radius: 14px;
  border: 1px solid #fff;
  box-sizing: border-box;
}

.disabled-btn {
  background: #E5E5E5;
  background:#E5E5E5;
  border:none;
  border-radius:0;
  height:50px;
  color:#999;
  line-height: 50px;
  font-size: 16px;

  &::after {
    content: none;
  }
}

.use-theme-color {
  color: inherit;
  background-color: transparent;
}
