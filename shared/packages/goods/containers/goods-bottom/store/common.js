import get from '@youzan/weapp-utils/lib/get';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';

// 校验商品状态
export const checkGoodsStatus = (state, getters) => {
  const { goods = {}, display = {} } = state;
  if (getters.goodsStockNum === 0) {
    return '该商品售罄啦，看看其他商品吧！';
  }
  if (goods.isDisplay !== 1 && display.showForbidBuyBtn) {
    return '该商品下架啦，看看其他商品吧！';
  }
  if (display.showForbidBuyBtn) {
    return goods.forbidBuyReason || '';
    // '(⊙o⊙) ~ 该商品暂时不可购买，看看其他商品吧！';
  }
  return '';
};

// 即将开售展示的样式
const getWaitToSoldViewConfig = (state) => {
  // 虚拟商品不支持购物车
  // const isVirtual = get(state, 'goods.isVirtual', false);
  // const hideShoppingCart = +get(state, 'shopConfig.hideShoppingCart', 0) !== 1;
  // const showCartBtn = !hideShoppingCart && !isVirtual;
  const showCartBtn = get(state, 'display.showCartBtn', false);

  let text = '';
  const skuScene = SKU_SCENE.ADD_CART;
  const accountUnionScene = ACCOUNT_UNION_SCENE.ADD_CART;
  if (showCartBtn) {
    text = '加入购物车';
  } else {
    text = '即将开售';
  }

  return {
    vice: {
      show: false
    },
    main: {
      show: true,
      disabled: !showCartBtn,
      text,
      skuScene,
      accountUnionScene,
    }
  };
};

// vip 限定购买 ??
const getLimitBuyViewConfig = (state) => {
  return {
    main: {
      show: get(state, 'display.showBuyBtn', false),
      text: '仅限特定会员购买',
    }
  };
};

// 初始化BottomView对象
export const getBottomViewConfig = (state, getters) => {
  const statusText = checkGoodsStatus(state, getters);
  if (statusText !== '') {
    return {
      statusBar: {
        text: statusText,
      }
    };
  }
  if (get(state, 'goods.waitToSoldTime')) {
    // 即将开售
    return getWaitToSoldViewConfig(state);
  }
  if (get(state, 'goods.limitBuy')) {
    // 仅限特定会员购买
    return getLimitBuyViewConfig(state);
  }
  return null;
};

export const getEmptyBottomViewConfig = () => ({});
