/* eslint-disable no-continue */
import get from '@youzan/weapp-utils/lib/get';
import args from '@youzan/weapp-utils/lib/args';
import { SKU_SCENE, ACCOUNT_UNION_SCENE } from 'shared/packages/goods/common/constant';

import {
  getBottomViewConfig,
} from './common';

const app = getApp();
const p = 'packages';

// 预售按钮展示的样式
const getPreSaleViewConfig = (state, getters) => {
  const btnInfo = getBottomViewConfig(state, getters);
  if (btnInfo) {
    return btnInfo;
  }
  const activity = get(state, 'goodsActivity.presale', {});

  const currentTime = +new Date();

  // 0：全款预售 1：定金预售
  const isDepositPresale = getters.isDepositPresale;

  let text = getters.buttomBuyButtonText;
  let path = null;
  let navigate = null;
  let switchTab = null;

  const { desc, subDesc } = getters.presaleDesc;

  if (currentTime < activity.preStartTime) {
    // 活动未开始
    return {
      statusBar: {
        text: '活动未开始',
      }
    };
  }

  const { endTime } = getters.countdown;
  if (currentTime > endTime) {
    return {
      statusBar: {
        text: '活动已结束',
      }
    };
  }

  if (isDepositPresale) {
    text = '支付定金';
  }

  const {
    isDepositPresaleHelpStart,
    isTailPayStart,
    isDepositPresaleHelpPayed,
    isHelpDepositPresale
  } = getters;

  if (isHelpDepositPresale) {
    const helpDepositExpansion = get(state, 'goodsActivity.presale.helpDepositExpansion', {});
    const {
      voucherId, activityId, joinActivityStatus, orderNo
    } = helpDepositExpansion;

    if (isDepositPresaleHelpStart || isTailPayStart) {
      if (isTailPayStart) {
        text = '支付尾款';
      } else {
        text = '邀请助力';
      }

      path = `/${p}/ump/handsel-expand/index?voucherId=${voucherId}&kdt_id=${getters.kdtId}&activityId=${activityId}`;
      navigate = true;
    }

    // 如果已经生成订单，但是还没有支付的话，需要跳转到订单页
    if (+joinActivityStatus === 0 && orderNo) {
      path = `/${p}/trade/order/result/index?orderNo=${orderNo}`;
      navigate = true;
    }
  }

  if (isDepositPresaleHelpPayed) {
    text = '已完成购买，看看其他商品';
    path = `/${p}/home/<USER>/index`;
    switchTab = true;
  }

  const btnMain = {
    text,
    path,
    navigate,
    switchTab,
    buy: true,
    show: true,
    loading: false,
    left: !isDepositPresaleHelpPayed && {
      main: {
        text: desc,
        class: 'normal align-left'
      },
      sub: subDesc
    } || null,
    hasLongText: true,
    skuScene: isHelpDepositPresale ? SKU_SCENE.ACT_BUY : SKU_SCENE.NORMAL_BUY,
    accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
  };
  const btnVice = {
    show: false,
  };
  return {
    main: btnMain,
    vice: btnVice,
  };
};

// 礼品按钮展示的样式
const getGiftViewConfig = (state, getters) => {
  const btnInfo = getBottomViewConfig(state, getters);
  if (btnInfo) {
    return btnInfo;
  }
  const btnMain = {
    show: true,
    text: '我要送礼',
    skuScene: SKU_SCENE.GIFT_BUY,
    accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
  };
  const btnVice = {
    show: false,
  };
  return {
    main: btnMain,
    vice: btnVice,
  };
};

export default {
  isGoodsBottomShow(state, getters) {
    return getters.saasGoodsBottomShow;
  },
  // 首页，店铺
  homeBtnView(state) {
    const showMiniShopBtn = get(state, 'display.showMiniShopBtn', false);
    const isGift = get(state, 'goodsActivity.gift', false);
    return {
      show: showMiniShopBtn && !isGift, // isSetHome 是否显示店铺首页
      url: `/${p}/home/<USER>/index`,
    };
  },
  // 客服
  imBtnView(state) {
    const showImBtn = get(state, 'display.showImBtn', false);
    const { goods = {}, sku = {}, picture = {}, multistore } = state;
    const isGift = get(state, 'goodsActivity.gift', false);

    const sourceParam = {
      kdt_id: app.getKdtId(),
      source: 'goods', // 写死'goods'
      endpoint: 'wx', // 渠道，wap端写死'h5'
      detail: JSON.stringify({
        alias: goods.alias,
        name: goods.title, // 商品name
        price: sku.price, // 商品价格，已原价格为主
        imgs: (picture.pictures || []).map((item) => item.url).slice(0, 2),
      })
    };

    if (multistore.id) {
      sourceParam.site_id = multistore.id; // 网点id
    }

    return {
      show: showImBtn && !isGift, // isSetContact 是否显示客服
      sourceParam: JSON.stringify(sourceParam),
    };
  },
  // 购物车
  miniCartBtnView(state) {
    const showMiniCartBtn = get(state, 'display.showMiniCartBtn', false);
    const isGift = get(state, 'goodsActivity.gift', false);
    const count = get(state, 'cartInfo.count', 0);
    const size = get(state, 'cartInfo.size', 0);
    const url = `/${p}/goods/cart/index`;

    return {
      show: showMiniCartBtn && !isGift, // isSetCartBtn 是否显示购物车
      count,
      size,
      url
    };
  },
  giftBtnView(state) {
    const showGiftBtn = get(state, 'display.showGiftBtn', false);
    const isGift = get(state, 'goodsActivity.gift', false);
    // 小程序暂不支持旧版我要送礼
    const isWeappSupport = false;
    return {
      show: showGiftBtn && !isGift && isWeappSupport,
    };
  },
  // 大按钮组合，包括vice和main，以及状态栏
  goodsCommonBottomView(state, getters) {
    let btnInfo = getBottomViewConfig(state, getters);
    if (btnInfo) {
      return btnInfo;
    }
    if (get(state, 'goodsActivity.presale', null)) {
      // 预售
      return getPreSaleViewConfig(state, getters);
    }
    if (get(state, 'goodsActivity.gift', null)) {
      // 礼品
      return getGiftViewConfig(state, getters);
    }
    btnInfo = {
      main: {
        text: getters.buttomBuyButtonText,
        show: get(state, 'display.showBuyBtn', false) && get(state, 'shopConfig.showBuyBtn', false),
        skuScene: SKU_SCENE.NORMAL_BUY,
        accountUnionScene: ACCOUNT_UNION_SCENE.NORMAL_BUY,
      },
      vice: {
        text: '加入购物车',
        show: get(state, 'display.showCartBtn', false),
        skuScene: SKU_SCENE.ADD_CART,
        accountUnionScene: ACCOUNT_UNION_SCENE.ADD_CART,
      }
    };
    // 如果购买按钮和加入购物车都不展示，显示购买按钮
    if (!btnInfo.main.show && !btnInfo.vice.show) {
      btnInfo.main.show = true;
    }
    return btnInfo;
  },

  goodsBottomView(state, getters) {
    return getters.goodsCommonBottomView;
  },

  // 按钮顶部展示tip
  goodsBottomTip() {
    return '';
  },
  // 店铺风险提示
  // { match, note }
  shopRisk(state) {
    return state.goods.risk || {
      match: false,
      note: '',
    };
  },
  bookEvent() {
    // 虚函数，诸如秒杀预约会重写
    return {};
  },
  // 联系客服卡片额外参数，表示卡片来源，这里是商品页
  imFrom() {
    return 'GOODS';
  },
  // 联系客服卡片特殊路径，必须包含 alias，price，originPrice，imFrom
  imPath(state, getters) {
    const { imFrom, goodsPrice } = getters;
    const basePath = get(state, 'goods.path', '');
    // 获取路径上的alias，因为秒杀需要活动alias，这样秒杀无需做特殊处理
    const alias = get(state, 'pageParams.alias', '');
    return args.add(basePath, {
      imFrom,
      alias,
      price: goodsPrice.currentPrice,
      originPrice: goodsPrice.originPrice,
    });
  },
  /**
   * 如果产品要求其他主题色也要支持渐变，
   * 让他找下设计出具体主题色到渐变色的规则，而不是列举
   * @param {*} state
   * @param {*} getters
   * @param {*} rootState
   */
  viceGradient(state, getters, rootState) {
    const { theme = {} } = rootState.shop || {};
    const gradient = {};

    if (theme.type === 13) {
      gradient.be = true;
      gradient.start = '#ffd01e';
      gradient.end = '#ff8917';
      gradient.deg = '90';
    }
    return gradient;
  },

  mainGradient(state, getters, rootState) {
    const { theme = {} } = rootState.shop || {};
    const gradient = {};
    if (theme.type === 13) {
      gradient.be = true;
      gradient.start = '#ff6034';
      gradient.end = '#ee0a24';
      gradient.deg = '90';
    }
    return gradient;
  }
};

// 可配置项
// buy: true,
// show: true,
// cart: true,
// quota: true, // 是否显示限购
// text:
// isUmp: false,
