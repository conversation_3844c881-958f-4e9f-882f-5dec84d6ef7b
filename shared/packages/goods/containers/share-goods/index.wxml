<canvas style="width: 375px; height: 667px; display: {{ showCanvas ? 'block' : 'none' }}" class="share-canvas" canvas-id="{{ canvasId }}" />

<!-- 分享遮罩层 -->
<van-popup
  show="{{ showModal }}"
  z-index="{{ 999 }}"
  custom-class="share-image__popup"
>
  <view class="share-image__container">
    <view class="share-image__close-btn" bindtap="closeShareImageModal" />
    <image
      src="{{ src }}"
      class="share-image--preview"
    />
    <!-- <view class="share-image__info">保存图片后，可分享到朋友圈</view> -->
    <view class="share-image__btn">
      <theme-view
        custom-class="zan-btn zan-btn--danger"
        bg="main"
        color="main-text"
        borderAfter="main"
        bindtap="clickSaveImage"
      >
        保存图片
      </theme-view>
    </view>
  </view>
</van-popup>

<van-action-sheet
  z-index="300"
  show="{{ sharePopShow }}"
  close-on-click-overlay="{{ closeOnClickOverlay }}"
  bind:close="closeActionSheet"
  bind:cancel="closeActionSheet" >
  <view>
    <button
      wx:if="{{ isSupportOpenBusinessView && recommendGoodThings && virtualTicket.validityType !== 3 }}"
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top action-recommend"
      bind:tap="handleRecommend"
    >
      <view class="big-word"><view class="recommend-icon icon-like-bold"></view>推荐好物</view>
      <view class="small-word">(分享到好物圈)</view>
    </button>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top"
      open-type="share"
      bind:tap="handleFriends"
    >
      发送给朋友
    </button>
    <button
      hover-class="action-sheet__item--hover"
      class="action-sheet__item van-hairline--top"
      bind:tap="handlePoster"
    >
      生成海报
    </button>
  </view>
  <view
    class="action-sheet__cancel"
    hover-class="action-sheet__cancel--hover"
    hover-stay-time="70"
    bind:tap="closeActionSheet"
  >
    取消
  </view>
</van-action-sheet>
