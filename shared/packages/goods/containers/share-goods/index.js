import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapMutations, mapGetters } from '@youzan/vanx';
import authorize from 'shared/utils/authorize';
import getApp from 'shared/utils/get-safe-app';
import args from '@youzan/weapp-utils/lib/args';
import get from '@youzan/weapp-utils/lib/get';
import { toHttps } from 'shared/utils/url';
import cdnImage, { getResponseImage } from '@youzan/weapp-utils/lib/cdn-image';
import { fetchUltraCode } from 'shared/utils/qrcode';
import logv3 from 'utils/log/logv3';
import { loadImage } from '../../store/utils/helpers/network';
// 将传入的分为单位的价格进行格式化
import { splitPrice } from '../../store/utils/helpers';
import { getSalesmanParamsObject } from 'shared/utils/salesman-params-handler';

const app = getApp();

// 秒杀分享也迁移Node后，做代码清理
VanxComponent({
  mapData: {
    ...mapState('/', {
      recommendGoodThings: (state) => state.shop.recommend_good_things,
    }),
    ...mapState({
      sharePopShow: (state) => state.displayPop.sharePopShow,
      title: (state) => get(state, 'goods.title'),
      path: (state) => get(state, 'goods.path'),
      alias: (state) => get(state, 'goods.alias'),
      goodsId: (state) => get(state, 'goods.id'),
      goodsImage: (state) => get(state, 'picture.pictures[0]'),
      goodsImageList: (state) => get(state, 'goods.pictures', []),
      salesmanAlias: (state) => get(state, 'salesman.alias'),
      // 秒杀
      seckill: (state) => get(state, 'marketActivity.seckill', null),
      // 拼团
      groupOn: (state) => get(state, 'marketActivity.groupOn', null),
      // 团购
      tuan: (state) => get(state, 'marketActivity.tuan', null),
      // 会员折扣
      discount: (state) => get(state, 'orderActivity.discount', null),
      // 限时折扣
      timelimitedDiscount: (state) => get(state, 'orderActivity.timelimitedDiscount', null),
      // 电子卡券
      virtualTicket: (state) => get(state, 'goodsActivity.virtualTicket', {}),
      originPrice: (state) => state.sku.oldPrice || state.goods.origin || state.sku.price,
      showPrice: (state) => state.sku.price,
      minPrice: (state) => splitPrice(state.sku.minPrice),
    }),
    ...mapGetters([
      'goodsTipCashBackDesc',
    ]),
  },

  data: {
    canvasId: 'drawerImage',
    // 控制预览蒙层是否展示
    showModal: false,
    // 控制 canvas 是否展示
    showCanvas: false,
    // 商品图临时路径
    img: '',
    // canvas 绘制的分享图临时路径
    src: '',
    // 小程序码图片路径
    weappCode: '',
    closeOnClickOverlay: true,
    isSupportOpenBusinessView: !!wx.openBusinessView,
  },
  methods: {
    ...mapMutations(['hideSharePop']),
    /**
     * @deprecated 临时方法
     * @description 直接生成销售员海报
     */
    drawSalemanCard() {
      this.doDrawPoster();
    },

    closeActionSheet() {
      this.hideSharePop();
      this.triggerEvent('finished');
    },

    handleFriends() {
      app.logger && app.logger.log({
        et: 'click',
        ei: 'goods_sharefriend',
        en: '点击分享给好友',
        si: '', // 店铺id，没有则置空
        params: {}
      });
    },

    doDrawPoster() {
      if (this.data.src) {
        this.closeActionSheet();
        this.successPoster(this.data.src);
        return;
      }

      wx.showLoading({ title: '正在生成' });
      // 秒杀暂时使用原先的海报生成
      if (this.data.seckill) {
        this.setYZData({
          showCanvas: true
        }, this.draw.bind(this));
      } else {
        this.makePoster();
      }
    },

    handlePoster() {
      app.logger && app.logger.log({
        et: 'click',
        ei: 'goods_photo',
        en: '点击生成海报',
        si: '', // 店铺id，没有则置空
        params: {}
      });

      this.doDrawPoster();
    },

    handleRecommend() {
      this.shareGoodsRecommend();
      this.closeActionSheet();
    },

    shareGoodsRecommend() {
      wx.showLoading({
        title: '正在同步',
        mask: true,
      });
      const openBusinessView = () => {
        wx.hideLoading();
        wx.openBusinessView({
          businessType: 'friendGoodsRecommend',
          extraData: {
            product: {
              item_code: this.shareGoodsId || this.data.goodsId,
              title: this.data.title,
              image_list: this.data.goodsImageList.map(it => it.url),
            }
          },
          fail(e) {
            const { errCode = -1 } = e;
            if (errCode !== 0 && errCode !== -3) {
              wx.showToast({
                title: '推荐失败' + JSON.stringify(e) + e.errCode,
                icon: 'none',
                duration: 2000
              });
            }
          }
        });
      };
      app.request({
        path: '/wscshop/goods/query-mall.json',
        data: {
          alias: this.data.alias,
        },
      }).then(res => {
        // 保留好物圈分享商品id
        this.shareGoodsId = res.goodsId;

        if (res.need_wait) {
          setTimeout(openBusinessView, 1500);
        } else {
          openBusinessView();
        }
      }).catch(() => {
        wx.hideLoading();
        wx.showToast({
          title: '同步失败',
          icon: 'none',
          duration: 2000
        });
      });
    },

    makePoster() {
      const dcPs = logv3.getDCPS();
      const offlineId = app.getOfflineId();
      const { chainStoreInfo = {} } = app.getShopInfoSync();
      const { isMultiOnlineShop } = chainStoreInfo;
      const kdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId(); // 连锁需要用总店的店铺ID，才能获取到小程序码

      const path = this.data.path.split('?')[0];
      let query = args.getAll(this.data.path);
      if (this.data.salesmanAlias) {
        query = { ...query, ...getSalesmanParamsObject({ sl: this.data.salesmanAlias }) };
      }
      // 参照生成QRcode的做法生成入参
      const scene = {
        share_from: 'poster',
        kdtId,
        page: path,
        guestKdtId: app.getKdtId(),
        ...query,
        dcPs,
        offlineId,
      };
      const data = {
        kdtId,
        scene: encodeURIComponent(JSON.stringify(scene)),
        page: 'pages/common/blank-page/index',
        sid: app.getSessionId(),
        alias: this.data.alias,
        isWeapp: 1,
      };
      if (query.activityId) {
        data.activityId = query.activityId;
        if (query.type) {
          data.activityType = query.type === 'helpcut' ? 'helpCut' : query.type;
        } else {
          data.activityType = query.activityType;
        }
      }
      if (offlineId) {
        data.offlineId = offlineId;
      }
      this.getPoster(data, true).then(res => {
        if (!res.value) {
          return Promise.reject('生成失败');
        }
        return loadImage(res.value);
      }).then(src => {
        wx.hideLoading();
        this.closeActionSheet();
        this.successPoster(src);
      }).catch(e => {
        this.failedPoster(e);
      });
    },

    getPoster(data, needRetry = false, retry = 0) {
      return app.request({
        path: '/wscshop/poster/goods.json',
        data: {
          retry,
          ...data,
        },
      }).catch(e => {
        if (needRetry) {
          return this.getPoster(data, false, 1);
        }
        return Promise.reject(e);
      });
    },

    successPoster(src) {
      this.setYZData({
        showModal: true,
        src
      }, () => {
        this.triggerEvent('success');
      });
    },

    failedPoster(e) {
      let errMsg = '';
      if (typeof e === 'object') {
        try {
          errMsg = JSON.stringify(e);
        } catch (err) {
          errMsg = String(e);
        }
      } else {
        errMsg = String(e);
      }
      wx.showToast({
        title: errMsg || '生成图片路径失败',
        icon: 'none',
      });
      app.getUserInfo((res) => {
        app.logger.appError({
          name: 'draw_goods_poster_error',
          message: '绘制商品海报失败',
          detail: {
            errMsg,
            userName: get(res, 'userInfo.nickName')
          }
        });
      });
      this.triggerEvent('failed', { err: e });
    },

    draw() {
      this.loadGoodsImage()
        .then(this.drawQrCode.bind(this))
        .then(this.createTempPath.bind(this))
        .then((src) => {
          this.successPoster(src);
        })
        .catch((e) => {
          this.failedPoster(e);
        })
        .then(() => {
          this.setYZData({ showCanvas: false });
          this.closeActionSheet();
          this.hideSharePop();
          this.triggerEvent('finished');
        });
    },

    drawQrCode() {
      if (this.data.weappCode) {
        return Promise.resolve();
      }
      if (!this.data.path) {
        return Promise.reject();
      }

      const path = this.data.path.split('?')[0];
      let query = args.getAll(this.data.path);

      if (this.data.salesmanAlias) {
        query = { ...query, ...getSalesmanParamsObject({ sl: this.data.salesmanAlias }) };
      }
      return fetchUltraCode(path, query).then((img) => {
        this.setYZData({
          weappCode: img
        });
        this.drawShareImage(img);
      });
    },

    // 生成分享图临时路径
    createTempPath() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvasId: this.data.canvasId,
            success: (res) => {
              resolve(res.tempFilePath);
            },
            fail: reject,
            complete: () => {
              wx.hideLoading();
              this.closeActionSheet();
            }
          }, this);
        }, 100);
      });
    },

    loadGoodsImage() {
      if (this.data.img) return Promise.resolve();
      if (!this.data.goodsImage) return Promise.reject();
      return new Promise((resolve, reject) => {
        const url = cdnImage(this.data.goodsImage.url, '!730x0.jpg');
        const imageUrl = toHttps(url);
        if (!imageUrl) {
          return wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000
          });
        }
        loadImage(imageUrl).then((img) => {
          this.setYZData({
            img,
          }, {
            cb() {
              resolve();
            },
            immediate: true,
          });
        }).catch((e) => {
          e && wx.showToast({
            title: '生成卡片失败:',
            icon: 'none',
            duration: 2000
          });
          reject();
        });
      });
    },

    // 预绘制和实际输出分享图绘制（ 通过有无 qrCodeUrl 判断 ）
    drawShareImage(qrCodeUrl) {
      const { img } = this.data;
      const ctx = wx.createCanvasContext(this.data.canvasId, this);

      const {
        top, left, height, width
      } = getResponseImage(this.data.goodsImage, 345, 405);

      ctx.setFillStyle('white');
      ctx.fillRect(0, 0, 375, 667);
      ctx.drawImage(img, 15 + left, 15 + top, width, height);
      // 最多两行的标题……
      ctx.setFontSize(18);
      ctx.setFillStyle('#333333');
      let title = this.data.title;
      let titleMore = '';
      const charLen = Math.floor(345 / 18);
      // 一行大约 23 个汉字需要换行
      if (title.length > charLen) {
        titleMore = title.slice(charLen);
        title = title.slice(0, charLen);
      }
      if (titleMore.length >= charLen) {
        titleMore = titleMore.slice(0, charLen - 1) + '...';
      }
      ctx.fillText(title, 15, 460);
      ctx.fillText(titleMore, 15, 485);

      // 价格展示
      ctx.setFontSize(12);
      ctx.setFillStyle('#9B9B9B');
      let {
        showPrice,
        originPrice = '',
        // price,
        minPrice,
        groupOn,
        seckill,
        tuan,
        goodsTipCashBackDesc,
        timelimitedDiscount,
        discount,
        // discountInvite,
      } = this.data;

      originPrice = originPrice.replace(/[￥¥]/, '');
      // 划线价格
      if (seckill) {
        // 秒杀
        ctx.setFillStyle('#FF4444');
        ctx.fillRect(15, 542, 50, 20);
        ctx.setFillStyle('#FFFFFF');
        ctx.fillText('秒杀价', 21, 557);
        ctx.setFillStyle('#FF4444');
        ctx.fillText('¥', 15, 605);
        ctx.setFontSize(28);
        ctx.fillText(seckill.sku.price, 30, 605);
        ctx.setFontSize(14);
        ctx.setFillStyle('#9B9B9B');
        ctx.fillText(`活动结束价: ¥${originPrice}`, 15, 627);
        ctx.fillRect(96, 621, (String(originPrice).length * 8) + 8, 1);
      } else if (groupOn) {
        // n 人拼团
        ctx.setFillStyle('#FF4444');
        ctx.fillRect(15, 542, 60 + (7 * String(groupOn.joinNum).length), 20);
        ctx.setFillStyle('#FFFFFF');
        ctx.fillText(`${groupOn.joinNum}人拼团价`, 21, 557);
        ctx.setFillStyle('#FF4444');
        ctx.fillText('¥', 15, 605);
        ctx.setFontSize(28);
        ctx.fillText(groupOn.sku.price, 30, 605);
        ctx.setFontSize(14);
        ctx.setFillStyle('#9B9B9B');
        ctx.fillText(`单买价: ¥${originPrice}`, 15, 627);
      } else if (tuan) {
        ctx.lineJoin = 'round';
        ctx.setFillStyle('#FF4444');
        ctx.fillRect(15, 545, 50, 20);
        ctx.setFontSize(12);
        ctx.setFillStyle('#FFFFFF');
        ctx.fillText('团购价', 21, 560);
        ctx.setFillStyle('#FF4444');
        ctx.setFontSize(14);
        ctx.fillText('¥', 15, 593);
        ctx.setFontSize(24);
        ctx.fillText(tuan.sku.price, 30, 593);
        ctx.setFontSize(12);
        ctx.setFillStyle('#9B9B9B');
        ctx.fillText(`¥${originPrice}`, 15, 613);
        ctx.fillRect(15, 608, ((String(originPrice).replace(/[.-]/g, '').length + 5) * 8), 1);
        if (goodsTipCashBackDesc) {
          ctx.setFontSize(16);
          ctx.fillText(`更享 ${goodsTipCashBackDesc}`, 15, 638);
        }
      } else if (timelimitedDiscount && originPrice) {
        ctx.lineJoin = 'round';
        ctx.setFillStyle('#FF4444');
        ctx.fillRect(15, 542, 72, 20);
        ctx.setFontSize(12);
        ctx.setFillStyle('#FFFFFF');
        ctx.fillText('限时折扣价', 21, 557);
        ctx.setFillStyle('#FF4444');
        ctx.setFontSize(14);
        ctx.fillText('¥', 15, 605);
        ctx.setFontSize(28);
        ctx.fillText(timelimitedDiscount.sku.price, 30, 605);
        ctx.setFontSize(14);
        ctx.setFillStyle('#9B9B9B');
        ctx.fillText(`活动结束价: ¥${originPrice}`, 15, 627);
      } else if (discount) {
        // 会员折扣 ??
        ctx.setFillStyle('#f44');
        ctx.fillText('¥', 15, 587);
        ctx.setFontSize(28);
        ctx.fillText(originPrice, 30, 587);
      } else {
        if (!showPrice) {
          showPrice = minPrice;
        }
        ctx.setFillStyle('#f44');
        ctx.fillText('¥', 15, 587);
        ctx.setFontSize(28);
        ctx.fillText(showPrice, 30, 587);
        if (originPrice && showPrice !== originPrice) {
          // 划线价格
          ctx.setFontSize(14);
          ctx.setFillStyle('#9B9B9B');
          ctx.fillText('¥' + originPrice, 15, 609);
          // line-through
          ctx.fillRect(15, 604, (originPrice.length * 9) + 6, 1);
        }
      }

      // 小程序码
      if (qrCodeUrl) {
        ctx.drawImage(qrCodeUrl, 275, 532, 80, 80);
      }

      ctx.setFontSize(9);
      ctx.setFillStyle('#9B9B9B');
      if (groupOn) {
        ctx.fillText('长按识别参团', 288, 627);
      } else {
        ctx.fillText('扫描或长按小程序码', 279, 627);
      }

      ctx.draw();
      this.triggerEvent(qrCodeUrl ? 'created' : 'inited', {
        canvasId: this.data.canvasId
      });
    },

    closeShareImageModal() {
      this.setYZData({
        showModal: false
      });
    },

    // 点击保存（ 成功后自动关闭弹层，抛出 saved 事件 ）
    clickSaveImage() {
      const { src } = this.data;
      if (!src) {
        return;
      }

      wx.showLoading({ title: '保存中' });

      app.logger && app.logger.log({
        et: 'click',
        ei: 'goods_savephoto',
        en: '点击海报保存按钮',
        si: '', // 店铺id，没有则置空
        params: {}
      });

      authorize('scope.writePhotosAlbum')
        .then(() => {
          this.saveShareImage(src)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
              });
              this.closeShareImageModal();
              this.triggerEvent('saved');
            }).catch(() => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
              });
            });
        })
        .catch(() => {
          wx.hideLoading();
          wx.showModal({
            content: '需要同意将分享图片保存到相册，点击确定后跳转至设置页操作',
            success: (e) => {
              if (e.cancel) return;
              wx.openSetting({
                success: ({ authSetting }) => {
                  if (authSetting['scope.writePhotosAlbum']) {
                    this.clickSaveImage();
                  }
                }
              });
            }
          });
        }).catch((err) => {
          console.log(err);
        });
    },

    // 保存图片 api 调用
    saveShareImage(tempFilePath) {
      return new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: resolve,
          fail: reject
        });
      });
    }
  },
});
