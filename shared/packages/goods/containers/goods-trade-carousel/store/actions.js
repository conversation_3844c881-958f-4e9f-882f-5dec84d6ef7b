import {
  node as request
} from 'shared/utils/request';

function fetchRecords(goodsId) {
  return request({
    path: '/wscgoods/getTransactionRecords.json',
    data: { goodsId }
  });
}

export default {
  fetchTradeRecords({ state, getters, commit }) {
    const goodsId = state.goods.id;

    if (!goodsId || !getters.showBuyRecordFloatStyle) {
      return;
    }

    fetchRecords(goodsId)
      .then(payload => {
        commit('setTradeRecords', payload);
      })
      .catch(() => {
        // do nothing
      });
  },
};
