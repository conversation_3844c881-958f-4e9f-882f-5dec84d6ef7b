import { getRandomAvatar } from './avatars';

function getTradeDesc({
  address = '',
  buyerName = '',
}) {
  return `${address}${buyerName || '**'}购买了该商品`;
}

export default {
  setTradeRecords(state, payload) {
    state.tradeRecordsCarousel = payload.map(tradeRecord => {
      tradeRecord.show = false;
      tradeRecord.avatar = getRandomAvatar();
      tradeRecord.desc = getTradeDesc(tradeRecord);
      return tradeRecord;
    });
  }
};
