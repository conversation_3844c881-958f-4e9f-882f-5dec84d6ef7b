<van-cell-group>
  <van-cell
    wx:if="{{ labels.length > 0 }}"
    value-class="review-tags"
  >
    <van-tag
      wx:for="{{ labels }}"
      wx:for-item="label"
      wx:key="id"
      round
      color="#FFF5F5"
      custom-class="review-tag"
    >
      {{ label.name }} {{ label.count }}
    </van-tag>
  </van-cell>

  <van-cell
    wx:for="{{ list }}"
    wx:for-item="item"
    wx:key="id"
  >
    <item-header
      rate="{{ item.score }}"
      avatar="{{ item.reviewerAvatar }}"
      nickname="{{ item.reviewerNickName }}"
    />

    <item-content
      content="{{ item.content }}"
      sku-desc="{{ item.specification }}"
      cdnImagesList="{{ item.cdnImagesList }}"
      bigImagesList="{{ item.bigImagesList }}"
    />
  </van-cell>
</van-cell-group>