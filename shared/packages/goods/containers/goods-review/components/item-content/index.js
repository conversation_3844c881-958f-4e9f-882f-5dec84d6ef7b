Component({
  properties: {
    content: String,
    skuDesc: String,
    bigImagesList: Array,
    cdnImagesList: Array,
  },

  methods: {
    previewImage({ target = {} }) {
      const { index } = target.dataset || {};

      if (index < 0) return;
      const { bigImagesList = [] } = this.data;
      wx.previewImage({
        urls: bigImagesList,
        current: bigImagesList[index]
      });
    }
  }
});
