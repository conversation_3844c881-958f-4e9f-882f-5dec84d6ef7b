import { mapState, mapGetters } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { node as request } from 'shared/utils/request';
import { EVALUATION } from 'shared/common/sensetive-words';
import get from '@youzan/weapp-utils/lib/get';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

VanxComponent({
  mapData: {
    ...mapGetters(['showCustomerReviews']),
    ...mapState(['goods']),
  },

  data: {
    review: {
      count: 0,
      reviews: [],
      labels: [],
    },
    reviewUrl: '',
    showReviewLink: false,
    reviewCellValue: '',
    reviewCellTitle: '宝贝' + EVALUATION
  },

  ready() {
    const showCustomerReviews = get(this.data, 'showCustomerReviews');
    const alias = get(this.data, 'goods.alias');
    if (showCustomerReviews) {
      this.fetchReviewDetail(alias);
    } else {
      this.updateReviewCellValue();
    }
  },

  methods: {
    fetchReviewDetail(alias) {
      request({
        path: 'wscshop/goods/review-info.json',
        data: { alias },
        config: {
          cache: true,
          expire: 30,
        }
      }).then((review) => {
        // 评价晒图要测一下
        const reviews = get(review, 'reviews', []);
        review.reviews = reviews.map(item => {
          if (Array.isArray(item.picturesList)) {
            item.cdnImagesList = item.picturesList.map(image => cdnImage(image, '!300x0.jpg'));
            item.bigImagesList = item.picturesList.map(image => cdnImage(image, '!730x0.jpg'));
          } else {
            item.cdnImagesList = [];
            item.bigImagesList = [];
          }
          return item;
        });
        this.setYZData({
          review,
        });
        this.updateReviewCellValue();
      });
    },
    updateReviewCellValue() {
      const count = get(this.data, 'review.count', 0);
      const alias = get(this.data, 'goods.alias');
      const reviewCellTitle = '宝贝' + EVALUATION + (count > 0 ? `(${count})` : '');
      const reviewCellValue = count > 0 ? '全部' + EVALUATION : '暂无' + EVALUATION;
      this.setYZData({
        reviewUrl: `/packages/evaluation/goods/list/index?alias=${alias}`,
        showReviewLink: count > 0,
        reviewCellTitle,
        reviewCellValue,
      });
    }
  }
});
