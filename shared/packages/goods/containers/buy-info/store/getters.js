import get from '@youzan/weapp-utils/lib/get';
import Config from 'shared/packages/goods/common/config';

export default {
  showBuyInfo(state, getters) {
    const {
      isOutLinkGoods,
      showBuyInfoSkuSelectorBar,
      showDeliveryBar,
      showBuyInfoDescriptionBar,
      showBuyInfoServiceBar,
    } = getters;
    return !isOutLinkGoods
    && (showBuyInfoSkuSelectorBar || showDeliveryBar || showBuyInfoDescriptionBar || showBuyInfoDescriptionBar || showBuyInfoServiceBar);
  },

  // 是否展示 skuBar
  showBuyInfoSkuSelectorBar(state) {
    const { sku, goods = {} } = state;
    const { itemSalePropList = [] } = goods;
    if ((sku.noneSku && itemSalePropList.length < 1) || !goods.isDisplay || sku.disabled) {
      return false;
    }

    const tuan = get(state, 'marketActivity.tuan');
    // 如果团购商品，并且活动没开始，直接返回
    if (tuan && tuan.startRemainTime > 0) {
      return false;
    }

    // 拼团商品和积分商品直接不显示
    const points = get(state, 'marketActivity.pointsExchange', null);
    if (points) {
      return false;
    }

    const groupOn = get(state, 'marketActivity.groupOn', null);
    if (groupOn) {
      return false;
    }

    return true;
  },

  showBuyInfoServiceBar(state) {
    const { supplier = {} } = state;
    return supplier.mode === Config.FX.PROMOTION;
  },

  showBuyInfoDescriptionBar(state) {
    const { haitao } = state;
    return haitao.isHaitao && haitao.haiTaoItemExtra;
  },

  // 是否 skuBar 不可选择
  isSkuBarDisabled(state, getters) {
    const { goods } = state;
    const { goodsStockNum, isShopClosed } = getters;

    if (isShopClosed || goods.isDisplay !== 1 || (goodsStockNum > 0)) {
      return true;
    }
    return false;
  },

  // skubar 文案
  buyInfoSkuBarText(state, getters) {
    const { selectedSkuComb } = state.skuExtraData;

    const selectedSku = state.skuExtraData.selectedSku || {};
    const selectedProp = state.skuExtraData.selectedProp || {};

    const showWords = [];
    const skuTree = get(getters, 'skuData.tree', []);
    skuTree.forEach(skuItem => {
      const skuKey = skuItem.kS;
      const skuVal = selectedSku[skuKey];
      if (selectedSkuComb && skuVal) {
        for (let i = 0; i < skuItem.v.length; i++) {
          const item = skuItem.v[i];
          if (item.id === skuVal) {
            showWords.push(`${item.name}`);
            return '';
          }
        }
      }
      if (!selectedSkuComb && !skuVal) {
        showWords.push(`${skuItem.k}`);
      }
    });

    const propList = get(state, 'goods.itemSalePropList', []);
    propList.forEach(propItem => {
      const propVal = selectedProp[propItem.kId] || [];
      if (selectedSkuComb && propVal.length > 0) {
        (propItem.v || []).forEach(it => {
          if (propVal.indexOf(it.id) > -1) {
            showWords.push(it.name);
          }
        });
      }
      if (!selectedSkuComb && propVal.length < 1) {
        showWords.push(propItem.k);
      }
    });
    if (selectedSkuComb) {
      return `已选：${showWords.join('  ')}`;
    }
    return `选择：${showWords.join('  ')}`;
  },

  // 是否展示物流配送栏
  showDeliveryBar(state, getters) {
    const { isVirtual } = state.goods;
    const { showDeliveryBar } = state.display;
    const { deliveryList } = getters;

    return showDeliveryBar && deliveryList.length > 0 && !isVirtual;
  },

  // 物流配送列表
  deliveryList(state) {
    const {
      supportExpress,
      supportSelfFetch,
      supportLocalDelivery,
      expressFee,
      localDeliveryFee
    } = state.distribution;
    if (!supportExpress && !supportLocalDelivery && !supportSelfFetch) return [];

    const expressFeeStr = expressFee === '免运费' ? expressFee : `运费：${expressFee}`;
    const localDeliveryFeeStr = localDeliveryFee ? `(${localDeliveryFee})` : '';
    const deliveryMap = {
      supportExpress: {
        name: '快递',
        detail: `快递发货(${expressFeeStr})`
      },
      supportLocalDelivery: {
        name: '同城送',
        detail: `同城配送${localDeliveryFeeStr}`
      },
      supportSelfFetch: {
        name: '自提',
        detail: '到店自提'
      }
    };
    const deliverySupportedMap = {
      supportLocalDelivery,
      supportExpress,
      supportSelfFetch
    };

    return Object.keys(deliverySupportedMap)
      .filter(key => deliverySupportedMap[key])
      .map(item => deliveryMap[item]);
  },

  // 物流栏展示内容
  deliveryBarText(state, getters) {
    const { deliveryList } = getters;
    const len = deliveryList.length;

    if (len === 1 && deliveryList[0].name === '同城送') {
      return `配送：${deliveryList[0].detail}`;
    }

    return getters.deliveryList.reduce((total, cur, curIndex) => {
      return total + (curIndex === len - 1 ? cur.name : `${cur.name}、`);
    }, '配送：');
  },

  // 海淘说明
  descriptionList(state) {
    const certList = [];
    const { haitao } = state;
    if (haitao && haitao.haiTaoItemExtra) {
      const { haiTaoItemExtra = {} } = haitao;
      if (haiTaoItemExtra.tariffRule === 0) {
        certList.push({
          tag: '单独计税',
          desc: Config.CERT_DESC.haitaoTaxExtraPays,
        });
      } else {
        certList.push({
          tag: '商品含税',
          desc: Config.CERT_DESC.haitaoTaxContains,
        });
      }
      certList.push({
        tag: '不支持7天无理由退货',
        desc: Config.CERT_DESC.haitaoUnsupportRefund,
      });
      const tradeMode = Config.HAITAO_MODE_MAP[haitao.haiTaoTradeMode];
      if (tradeMode) {
        certList.push({
          tag: tradeMode.modeStr,
          desc: tradeMode.modeDesc,
        });
      }
    }
    return certList;
  },

  // 海淘展示内容
  descriptionBarText(state, getters) {
    const { descriptionList = [] } = getters;
    return descriptionList.map(item => item.tag).join(' | ');
  },
};
