// import { storeMixin } from '@youzan/weapp-utils/lib/store';
import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapState, mapActions } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState([
      'goods',
      'haitao',
      'supplier',
      'priceCalendarData',
    ]),
    ...mapState({
      groupOn: (state) => state.marketActivity.groupOn,
      hasPriceCalendar: (state) => get(state, 'goodsActivity.virtualTicket.validityType', 0) === 3,
      selectedSkuDate: (state) => get(state, 'skuExtraData.selectedSkuDate', ''),
    }),
    ...mapGetters([
      'isSeckill',
      'buyInfoSkuBarText',
      'deliveryList',
      'deliveryBarText',
      'showBuyInfo',
      'showBuyInfoSkuSelectorBar',
      'showDeliveryBar',
      'showBuyInfoServiceBar',
      'showBuyInfoDescriptionBar'
    ]),
  },

  properties: {
    // calendarInfoData: {
    //   type: Object,
    //   value: {}
    // },
    // virtualForPriceCalendar: {
    //   type: Object,
    //   value: {}
    // }
  },

  data: {
  },

  methods: {
    ...mapActions([
      'showSkuPop',
    ]),
    toggleSku() {
      // 秒杀商品禁止点击商品规格
      if (this.data.isSeckill) {
        return;
      }
      // 这里只管显示，不管skuScene
      this.showSkuPop();
    }
  }
});
