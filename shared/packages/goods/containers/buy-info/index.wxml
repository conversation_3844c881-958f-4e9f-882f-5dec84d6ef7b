<van-cell-group
  wx:if="{{ showBuyInfo }}"
  class="goods-detail__panel"
  border="{{ false }}"
>
  <user-authorize
    wx:if="{{ showBuyInfoSkuSelectorBar && !hasPriceCalendar }}"
    bind:next="toggleSku"
    scene="click_buy_now"
  >
    <van-cell
      is-link
      border="{{ showDeliveryBar }}"
      title-width="42px"
      custom-class="cell--center"
      title-class="cell-title__class"
      value-class="cell-value__class"
    >
      <text class="multi-desc__cell--margin">{{ buyInfoSkuBarText }}</text>
    </van-cell>
  </user-authorize>

  <delivery-cell
    wx:if="{{ showDeliveryBar }}"
    list="{{ deliveryList }}"
    desc="{{ deliveryBarText }}"
  />

  <!-- 商品说明 -->
  <description-cell
    wx:if="{{ showBuyInfoDescriptionBar }}"
    hai-tao-trade-mode="{{ haitao.haiTaoTradeMode }}"
  />

  <!-- 服务 -->
  <service-cell
    wx:if="{{ showBuyInfoServiceBar }}"
    supplier-kdt-id="{{ supplier.kdtId }}"
  />
</van-cell-group>

<van-cell-group
  wx:if="{{ hasPriceCalendar }}"
  class="goods-detail__panel"
  border="{{ false }}">
  <van-cell
    wx:if="{{ showBuyInfoSkuSelectorBar }}"
    is-link
    title-width="42px"
    bindtap="toggleSku"
    custom-class="cell--center"
    title-class="cell-title__class"
    value-class="cell-value__class"
  >
    <text class="multi-desc__cell--margin">{{ buyInfoSkuBarText }}</text>
  </van-cell>
  <price-view
    date-has-select="{{ selectedSkuDate }}"
    price-show-date="{{ priceCalendarData.nearlyFourDayMarketableMap }}"
    bindtap="toggleSku"
  />
</van-cell-group>
