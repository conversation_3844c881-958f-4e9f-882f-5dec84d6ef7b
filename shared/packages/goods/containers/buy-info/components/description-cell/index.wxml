<van-cell
  is-link
  bind:click="showPopup"
>
  <view
    slot="title"
    class="description-bar__title"
  >
    <text class="label">商品说明 </text>
    <text class="value">{{ descriptionBarText }}</text>
  </view>
</van-cell>

<goods-popup
  show="{{ show }}"
  bind:tap:btn="hidePopup"
>
  <van-cell
    wx:for="{{ descriptionList }}"
    wx:key="{{ item.tag }}"
    title="{{ item.tag }}"
    label="{{ item.desc }}"
  />
</goods-popup>