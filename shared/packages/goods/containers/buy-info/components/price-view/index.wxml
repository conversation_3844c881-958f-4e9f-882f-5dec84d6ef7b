<wxs src="./index.wxs" module="tools" />

<!-- 日历价格显示 -->
<view class="price-calendar-cell">
  <view class="price-calendar-cell__container">
    <block wx:for="{{ priceShowDate }}" wx:key="{{ index }}">
      <theme-view 
        custom-class="price-calendar-cell__box"
        border="{{dateHasSelect == index ? 'general' : ''}}"
        data-index="{{Object.keys(priceShowDate)}}"
      >
        <view class="date">
          {{tools.delYear(index)}}
        </view>
        <view class="price">
          ￥{{tools.price(item)}}
        </view>
      </theme-view>
    </block>
  </view>
</view>
