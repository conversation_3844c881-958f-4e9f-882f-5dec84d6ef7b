import WscComponent from 'shared/common/base/wsc-component/index';

WscComponent({
  properties: {
    supplierKdtId: Number
  },

  methods: {
    showPopup() {
      this.setYZData({
        show: true
      }, {
        immediate: true
      });
    },

    hidePopup() {
      this.setYZData({
        show: false
      }, {
        immediate: true
      });
    },

    goToPage() {
      const supplierKdtId = this.data.supplierKdtId;

      wx.navigateTo({
        url: `/packages/shop/info/index?kdtId=${supplierKdtId}`,
      });
    }
  }
});
