import WscComponent from 'pages/common/wsc-component/index';
import throttle from 'utils/throttle';
import navigate from '@/helpers/navigate';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import StickyControl from 'shared/utils/sticky-control';
import pageScrollBehavior from 'shared/components/showcase/behaviors/page-scroll-control-behavior';
import getSystemInfo from 'shared/utils/browser/system-info';
import {
  TYPE_MAP, ICON_TEXT_MAP, LINK_URL_MAP, URL_PATTERN_MAP, PAGE_TYPE_MAP, EVENT_MAP, FORBID_SHARE_TYPE, SHARE_CARD_MAP, SHARE_CARD_TYPE_MAP
} from './constants';
import source from './source';
import IconController from './icon-controller/index';
import { isNewIphone } from 'shared/utils/browser/device-type';

const app = getApp();
const { system } = getSystemInfo();
const isAndroid = /android/i.test(system);
const isIphoneX = isNewIphone();
const FLOATING_MODE_GET_OUT = 2; // 悬浮模式。上滑隐藏，下滑出现

WscComponent({
  behaviors: [pageScrollBehavior],

  data: {
    active: false,
    hidden: false,
    typeMap: TYPE_MAP,
    iconTextMap: ICON_TEXT_MAP,
    mainFloatingNav: {},
    subFloatingNavs: [],
    normalList: [],
    independentList: [],
    skeletonNum: { line: 0, panel: 0 },
    isIphoneX,
    isTab: false,
    shareCard: '',
    showSharePop: false
  },

  attached() {
    const page = getCurPage();
    this.pageType = this.getPageType(page.route); // 获取页面类型 pageType

    this.on && this.on(EVENT_MAP.PULL_DOWN, this.handlePullDownRefresh); // 监听下拉刷新

    this.lastScrollTop = 0; // 设置初始 scrollTop

    this.justAttached = true; // 用于防止在自动下拉刷新时，重复请求数据

    Promise.all([source.getNavData(this.pageType), app.isSwitchTab()])
      .then(this.processNavData.bind(this));

    // 设置特殊分享卡片
    this.setYZData({
      shareCard: SHARE_CARD_MAP[this.pageType] || ''
    });
  },

  detached() {
    this.delPageScrollControlSubscribe(this.scrollHandler, this, this.pageKey);
    this.hasSetPageScroll = false;

    this.iconController && this.iconController.destroy(this);
  },

  methods: {
    onContactBack: navigate.contactBack,

    // 打开分享卡片
    openSharePop(shareCardPageType) {
      this.setYZData({
        shareCardPageType,
        showSharePop: true
      });
    },

    // 隐藏分享卡片
    hideSharePop() {
      this.setYZData({
        showSharePop: false
      });
    },

    // 点击主窗口
    handleMainNavTap() {
      this.changeActive(!this.data.active);
    },

    // 点击子窗口
    handleNavTap(ev) {
      const { navType } = ev.currentTarget.dataset;

      if (!navType) {
        return;
      }

      switch (navType) {
        case TYPE_MAP.BACK_TO_TOP: // 返回顶部
          StickyControl.releaseStickyControlMap();
          wx.pageScrollTo({
            scrollTop: 0,
            duration: isAndroid ? 0 : 300
          });
          break;
        case TYPE_MAP.SHARE: // 分享
          // 如果是主页或微页面，打开分享卡片
          if (this.pageType === PAGE_TYPE_MAP.HOME || this.pageType === PAGE_TYPE_MAP.FEATURE) {
            this.openSharePop(SHARE_CARD_TYPE_MAP[this.pageType]);
          }
          this.triggerEvent(EVENT_MAP.SHARE);
          break;
        case TYPE_MAP.HOME: // 主页
        case TYPE_MAP.CART: // 购物车
        case TYPE_MAP.USERCENTER: // 会员中心
          navigate.switchTab({
            url: LINK_URL_MAP[navType]
          });
          break;
        default:
          break;
      }

      this.data.active && this.changeActive(false); // 恢复原位
    },

    // 关闭弹层
    handleClosePanel() {
      this.changeActive(false);
    },

    // 更新：展开 or 收起
    changeActive(active, otherData = {}) {
      this.setYZData({ active, ...otherData });

      if (this.data.normalList.length < 4 && this.iconController) { // 只有在子窗口少于 4 个时（即竖排展开时），才更新分销员 icon 的位置
        this.iconController.setAlone(this, active);
      }
    },

    // 处理悬浮窗数据
    processNavData([data, isTab], isRefresh = false) {
      const { mainFloatingNav } = data;
      let { subFloatingNavs = [] } = data;

      // 在小程序需要过滤心愿单
      subFloatingNavs = subFloatingNavs.filter(({ navType }) => {
        // 过滤心愿单图标
        if (navType === TYPE_MAP.WISH) {
          return false;
        }

        // 主页过滤主页图标
        if (navType === TYPE_MAP.HOME) {
          return this.pageType !== PAGE_TYPE_MAP.HOME;
        }

        // 商品详情过滤购物车图标
        if (navType === TYPE_MAP.CART) {
          return this.pageType !== PAGE_TYPE_MAP.GOODS_DETAIL;
        }

        return true;
      });

      // 子窗口数量为 0（未开启悬浮窗 或 子悬浮窗数组为空）
      if (subFloatingNavs.length === 0) {
        this.changeActive(false, {
          hidden: false,
          isTab,
          subFloatingNavs: []
        });

        // 移除滚动监听
        this.delPageScrollControlSubscribe(this.scrollHandler, this, this.pageKey);
        this.hasSetPageScroll = false;

        // 设置位置
        this.iconController && this.iconController.setIcon(this, { priority: 100, height: 0 });
        return;
      }

      if (mainFloatingNav.floatingMode === FLOATING_MODE_GET_OUT) { // 监听滚动
        if (!this.hasSetPageScroll) {
          this.pageKey = this.setPageScrollControlSubscribe(this.scrollHandler, this);
          this.hasSetPageScroll = true;
        }
      } else if (this.hasSetPageScroll) {
        this.delPageScrollControlSubscribe(this.scrollHandler, this, this.pageKey);
        this.hasSetPageScroll = false;
      }

      // 对数据进行处理
      mainFloatingNav.foldedIconUrl = this.getCdnImageUrl(mainFloatingNav.foldedIconUrl);
      mainFloatingNav.unfoldedIconUrl = this.getCdnImageUrl(mainFloatingNav.unfoldedIconUrl);
      const { normalList, independentList } = this.getComputedSubNavList(subFloatingNavs);

      // 更新悬浮窗数据
      this.setYZData({
        hidden: false,
        isTab,
        mainFloatingNav,
        subFloatingNavs,
        normalList,
        independentList,
        skeletonNum: this.getSkeletonNum(normalList, independentList),
        forbidShare: FORBID_SHARE_TYPE[this.pageType] || false
      });

      // 获取购物车数据
      if (this.whereIsCart) { // 如果存在购物车悬浮窗
        this.on(EVENT_MAP.ADD_CART, this.handleAddCart); // 监听添加购物车
        source.getCartCount(isRefresh).then(count => {
          this.refreshCartIcon(+count > 0);
        });
      }

      // 调整分销员 icon 的位置
      const visibleNum = this.getVisibleNum(normalList, independentList); // 可见悬浮窗的数量

      this.iconController = new IconController()
        .setBase(this.getBasePosition(isTab, isIphoneX))
        .setIcon(this, {
          priority: 100,
          height: 58 * visibleNum - 10
        });
    },

    /**
     * 处理购物车数据
     * @param {Number} count 购物车商品数量
     * @param {Object} cartEvent “添加购物车”事件
     */
    handleAddCart(cartEvent = {}) {
      if (cartEvent.type === 'add') {
        source.addCartCount();
        this.refreshCartIcon(true);
      }
    },

    // 刷新购物车
    refreshCartIcon(hasGoods) {
      const [type, index] = this.whereIsCart; // 获取购物车的相关信息：【普通悬浮窗 or 独立悬浮窗】【下标位置】

      if (this.data.subFloatingNavs.length === 1) {
        this.setYZData({
          'subFloatingNavs[0].hasBadge': hasGoods
        });
      } else {
        this.setYZData({
          [`${type}List[${index}].hasBadge`]: hasGoods
        });
      }
    },

    // 下拉刷新
    handlePullDownRefresh() {
      if (this.justAttached) {
        this.justAttached = false;
        return;
      }

      // 如果下拉的不是组件所在的页面，则直接返回
      if (!this.iconController || this.iconController.id !== getCurPage().__wxExparserNodeId__) {
        return;
      }

      this.off(EVENT_MAP.ADD_CART, this.handleAddCart); // 移除添加购物车的监听

      source.getNavData(this.pageType, true).then(data => { // 获取新数据
        this.processNavData([data, this.data.isTab], true);
      });
    },

    // 对子窗口处理，便于页面展示
    getComputedSubNavList(subFloatingNavs) {
      const normalList = []; // 普通子窗口
      const independentList = []; // 独立子窗口
      this.whereIsCart = null;

      subFloatingNavs.forEach(nav => {
        if (nav.navType === TYPE_MAP.CART) { // 标志是否有购物车
          this.whereIsCart = nav.independent ? ['independent', independentList.length] : ['normal', normalList.length];
        }

        nav.iconUrl = this.getCdnImageUrl(nav.iconUrl); // 压缩图片

        if (nav.independent) {
          independentList.push(nav);
        } else {
          normalList.push(nav);
        }
      });

      return { normalList, independentList };
    },

    // 获取占位的数量
    getSkeletonNum(normalList, independentList) {
      let line = independentList.length;
      let panel = 0;

      if (normalList.length < 4) {
        line += normalList.length;
      } else {
        const num = 3 - normalList.length % 3;
        panel = num === 3 ? 0 : num;
      }

      return { line: 5 - line, panel };
    },

    // 获取未展开时页面可见的悬浮窗数量，用于调整分销员 icon 的位置
    getVisibleNum(normalList, independentList) {
      let num = independentList.length;

      if (normalList.length > 0) {
        num++;
      }

      return num;
    },

    getCdnImageUrl(url) {
      return cdnImage(url, '!150x150.png');
    },

    // 获取页面类型
    getPageType(url) {
      let pageType = 0;

      Object.keys(URL_PATTERN_MAP).some(key => {
        return URL_PATTERN_MAP[key].some(p => {
          if (p.test(url)) {
            pageType = key;
            return true;
          }
          return false;
        });
      });

      return Number(pageType);
    },

    // 获取基础位置
    getBasePosition(isTab, isIphoneX) {
      // if (isTab) {
      //   return 30;
      // }
      if (isIphoneX) {
        return 114;
      }
      return 80;
    },

    scrollHandler(ev) {
      if (!this.__handleScroll) {
        this.__handleScroll = throttle(this.handleScroll, 300);
      }
      this.__handleScroll(ev);
    },

    handleScroll({ scrollTop }) {
      const { active, hidden } = this.data;
      const offset = scrollTop - this.lastScrollTop;

      if (scrollTop <= 50) {
        this.lastScrollTop = scrollTop;
        hidden && this.setYZData({ hidden: false });
        return;
      }

      if (!hidden) {
        if (offset < 0) {
          this.lastScrollTop = scrollTop;
        } else if (offset > 50) {
          !hidden && this.setYZData({ hidden: true });
          active && this.changeActive(false);
          this.lastScrollTop = scrollTop;
        }
      } else {
        this.lastScrollTop = scrollTop;
        if (offset < 0 && hidden) {
          this.setYZData({ hidden: false });
        }
      }
    }
  }
});

// 获取当前页面
function getCurPage() {
  const ps = getCurrentPages();
  return ps[ps.length - 1];
}
