export const TYPE_MAP = {
  HOME: 1,
  CART: 2,
  WISH: 3,
  CONTACT: 4,
  SHARE: 5,
  USERCENTER: 6,
  BACK_TO_TOP: 7,
  SCAN: 8,
};

export const ICON_TEXT_MAP = {
  1: '主页',
  2: '购物车',
  3: '心愿单',
  4: '客服',
  5: '分享',
  6: '个人中心',
  7: '顶部',
  8: '扫一扫',
};

const p = 'packages';

export const LINK_URL_MAP = {
  1: `/${p}/home/<USER>/index`,
  2: `/${p}/goods/cart/index`,
  6: `/${p}/usercenter/dashboard/index`,
};

export const URL_PATTERN_MAP = {
  1: [/^(packages|pages)\/home\/dashboard/, /^(packages|pages)\/home\/tab/],
  3: [/^(packages|pages)\/home\/feature/],
  8: [
    /^pages\/goods\/detail/,
    /^packages\/goods(:?-v\d+)?\/detail/,
    /^packages\/goods(:?-v\d+)?\/seckill/,
  ],
  9: [/^packages\/shop\/goods\/all/, /^packages\/shop\/goods\/group/],
};

export const PAGE_TYPE_MAP = {
  HOME: 1,
  FEATURE: 3,
  GOODS_DETAIL: 8,
  GOODS_GROUP: 9,
};

export const EVENT_MAP = {
  ADD_CART: 'component:sku:cart',
  PULL_DOWN: 'home:refresh',
  SHARE: 'share',
};

export const FORBID_SHARE_TYPE = {
  1: true,
  3: true,
  8: true,
  9: false,
};

export const SHARE_CARD_MAP = {
  1: 'share-feature',
  3: 'share-feature',
};

export const SHARE_CARD_TYPE_MAP = {
  1: 'homepage',
  3: 'micropage',
};
