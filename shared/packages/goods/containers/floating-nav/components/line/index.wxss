.sub {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);
  opacity: 0;
  visibility: hidden;
  transition: all 0.24s;
}

.sub::after {
  display: none;
}

/*
==== 独立子窗口
*/
.independent {
  opacity: 1;
  visibility: visible;
}

.independent-1 {
  transform: translate3d(0, -116px, 0);
}

.independent-2 {
  transform: translate3d(0, -58px, 0);
}

.line--only-independent .independent-1 {
  transform: translate3d(0, -58px, 0);
}

.line--only-independent .independent-2 {
  transform: none;
}

/*
==== 占位符
*/
.skeleton {
  display: none;
}

/*
==== 角标
*/
.has-badge::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  border: 1px solid white;
  border-radius: 50%;
  background-color: #f44;
}

/*
==== 竖排展开
*/
.active .sub {
  opacity: 1;
  visibility: visible;
}

.active .sub:nth-child(1) {
  transform: translate3d(0, -290px, 0);
}

.active .sub:nth-child(2) {
  transform: translate3d(0, -232px, 0);
}

.active .sub:nth-child(3) {
  transform: translate3d(0, -174px, 0);
}

.active .sub:nth-child(4) {
  transform: translate3d(0, -116px, 0);
}

.active .sub:nth-child(5) {
  transform: translate3d(0, -58px, 0);
}
