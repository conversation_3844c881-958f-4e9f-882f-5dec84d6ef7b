.panel {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  bottom: 100px;
  left: 50%;
  margin-left: -208rpx;
  width: 416rpx;
  padding: 20rpx 8rpx 0;
  border-radius: 24px;
  background-color: rgba(0,0,0,.7);
  opacity: 0;
  visibility: hidden;
  transform: translate3d(36vw, 50%, 0) scale3d(0,0,1);
  z-index: 3;
  transition: all 0.24s;
}

.is-tab .panel {
  bottom: 50px;
}

.panel-item {
  flex: 1 1 30%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 30%;
  margin-bottom: 20rpx;
  height: 128rpx;
  background: transparent;
  font-size: 12px;
  line-height: 16px;
  color: white;
}

.panel-item::after {
  display: none;
}

.panel--three-rows {
  width: 456rpx;
  margin-left: -228rpx;
  padding: 16rpx 8rpx 6rpx;
}

.panel--three-rows .panel-item {
  margin-bottom: 8rpx;
}

.sub {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  transition: all 0.24s;
}

.outer-space {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

/*
==== 角标
*/
.has-badge::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  border: 1px solid white;
  border-radius: 50%;
  background-color: #f44;
}

/*
==== 展开
*/
.active .panel {
  opacity: 1;
  visibility: visible;
  transform: none;
}

.active .outer-space {
  width: 100%;
  height: 100%;
  z-index: 2;
}
