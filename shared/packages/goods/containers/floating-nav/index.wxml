<view
  wx:if="{{ subFloatingNavs.length > 0 }}"
  class="theme-floating-nav {{ active ? 'active' : '' }} {{ hidden ? 'hidden' : '' }} {{ isIphoneX ? 'is-iphone-x' : '' }}"
>
  <!-- 只有一个子窗口 -->
  <block wx:if="{{ subFloatingNavs.length === 1 }}">
    <button
      wx:if="{{ subFloatingNavs[0].navType === typeMap.SHARE || subFloatingNavs[0].navType === typeMap.CONTACT }}"
      class="item main"
      hover-class="none"
      style="background-image: url({{ subFloatingNavs[0].iconUrl }});"
      open-type="{{ subFloatingNavs[0].navType === typeMap.CONTACT ? 'contact' : (forbidShare ? '' : 'share') }}"
      bindcontact="onContactBack"
      data-nav-type="{{ subFloatingNavs[0].navType }}"
      bindtap="handleNavTap"
    />
    <view
      wx:else
      class="item main {{ subFloatingNavs[0].hasBadge ? 'has-badge' : '' }}"
      style="background-image: url({{ subFloatingNavs[0].iconUrl }});"
      data-nav-type="{{ subFloatingNavs[0].navType }}"
      bindtap="handleNavTap"
    />
  </block>

  <!-- 多于一个子窗口 -->
  <block wx:else>
    <view wx:if="{{ normalList.length > 0 }}" bindtap="handleMainNavTap">
      <view class="item main main-1" style="background-image: url({{ mainFloatingNav.foldedIconUrl }});" />
      <view class="item main main-2" style="background-image: url({{ mainFloatingNav.unfoldedIconUrl }});" />
    </view>

    <!-- 竖排 -->
    <view class="line {{ normalList.length === 0 ? 'line--only-independent' : '' }}" >
      <view wx:for="{{ skeletonNum.line }}" wx:key="{{ index }}" class="sub skeleton"/>

      <block wx:for="{{ independentList }}" wx:key="{{ index }}">
        <template is="sub-item" data="{{ item, index: independentList.length === 1 ? 2 : index + 1, forbidShare, typeMap }}" />
      </block>

      <block wx:if="{{ normalList.length <= 3 }}">
        <block wx:for="{{ normalList }}" wx:key="{{ index }}">
          <template is="sub-item" data="{{ item, index, forbidShare, typeMap }}" />
        </block>
      </block>
    </view>

    <!-- 面板 -->
    <block wx:if="{{ normalList.length > 3 }}">
      <view class="outer-space" bindtouchstart="handleClosePanel" />

      <view class="panel {{ normalList.length > 6 ? 'panel--three-rows' : '' }}">
        <block wx:for="{{ normalList }}" wx:key="{{ index }}">
          <button
            wx:if="{{ item.navType === typeMap.SHARE || item.navType === typeMap.CONTACT }}"
            class="panel-item"
            hover-class="none"
            open-type="{{ item.navType === typeMap.CONTACT ? 'contact' : (forbidShare ? '' : 'share') }}"
            data-nav-type="{{ item.navType }}"
            bindtap="handleNavTap"
            bindcontact="onContactBack"
          >
            <template is="panel-item" data="{{ item, iconTextMap }}" />
          </button>

          <view
            wx:else
            class="panel-item"
            data-nav-type="{{ item.navType }}"
            bindtap="handleNavTap"
          >
            <template is="panel-item" data="{{ item, iconTextMap }}" />
          </view>
        </block>

        <view
          wx:for="{{ skeletonNum.panel }}"
          wx:key="{{ index }}"
          class="panel-item"
        />
      </view>
    </block>
  </block>

  <!-- 特殊分享卡片 -->
  <share-feature
    wx:if="{{ shareCard === 'share-feature' }}"
    page-type="{{ shareCardPageType }}"
    show-share-pop="{{ showSharePop }}"
    bind:finished="hideSharePop"
  />
</view>

<template name="sub-item">
  <button
    wx:if="{{ item.navType === typeMap.SHARE || item.navType === typeMap.CONTACT }}"
    class="item sub {{ item.independent ? 'independent independent-' + index : '' }}"
    hover-class="none"
    style="background-image: url({{ item.iconUrl }});"
    open-type="{{ item.navType === typeMap.CONTACT ? 'contact' : (forbidShare ? '' : 'share') }}"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
    bindcontact="onContactBack"
  />
  <view
    wx:else
    class="item sub {{ item.independent ? 'independent independent-' + index : '' }} {{ item.hasBadge ? 'has-badge' : '' }}"
    style="background-image: url({{ item.iconUrl }});"
    data-nav-type="{{ item.navType }}"
    bindtap="handleNavTap"
  />
</template>

<template name="panel-item">
  <view
    class="item sub {{ item.hasBadge ? 'has-badge' : '' }}"
    style="background-image: url({{ item.iconUrl }});"
  />
  {{ iconTextMap[item.navType] }}
</template>
