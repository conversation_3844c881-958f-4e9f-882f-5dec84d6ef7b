.theme-floating-nav {
  position: fixed;
  bottom: 80px;
  right: 10px;
  width: 48px;
  height: 48px;
  z-index: 10;
  transition: all 0.24s;
}

.is-iphone-x {
  bottom: 114px;
}

.is-tab {
  bottom: 30px;
}

.hidden {
  transform: translate3d(100px, 0, 0);
}

/*
==== 子窗口
*/
.item {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,.16);
  transition: all 0.24s;
}

.item::after {
  display: none;
}

.main {
  z-index: 1;
}

.main-2 {
  opacity: 0;
  visibility: hidden;
  transform: rotate(-90deg);
}

.sub {
  opacity: 0;
  visibility: hidden;
}

/*
==== 独立子窗口
*/
.independent {
  opacity: 1;
  visibility: visible;
}

.independent-1 {
  transform: translate3d(0, -116px, 0);
}

.independent-2 {
  transform: translate3d(0, -58px, 0);
}

.line--only-independent .independent-1 {
  transform: translate3d(0, -58px, 0);
}

.line--only-independent .independent-2 {
  transform: none;
}

/*
==== 占位符
*/
.skeleton {
  display: none;
}

/*
==== 角标
*/
.has-badge::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  border: 1px solid white;
  border-radius: 50%;
  background-color: #f44;
}

/*
==== 竖排展开
*/
.active .main-1 {
  opacity: 0;
  visibility: hidden;
  transform: rotate(90deg);
}

.active .main-2 {
  opacity: 1;
  visibility: visible;
  transform: none;
}

.active .sub {
  opacity: 1;
  visibility: visible;
}

.active .line .sub:nth-child(1) {
  transform: translate3d(0, -290px, 0);
}

.active .line .sub:nth-child(2) {
  transform: translate3d(0, -232px, 0);
}

.active .line .sub:nth-child(3) {
  transform: translate3d(0, -174px, 0);
}

.active .line .sub:nth-child(4) {
  transform: translate3d(0, -116px, 0);
}

.active .line .sub:nth-child(5) {
  transform: translate3d(0, -58px, 0);
}

/*
==== 面板弹窗
*/
.panel {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  bottom: 100px;
  left: 50%;
  margin-left: -208rpx;
  width: 416rpx;
  padding: 20rpx 8rpx 0;
  border-radius: 24px;
  background-color: rgba(0,0,0,.7);
  opacity: 0;
  visibility: hidden;
  transform: translate3d(36vw, 158rpx, 0) scale3d(0, 0, 1);
  z-index: 3;
  transition: all 0.2s;
}

.panel--three-rows {
  width: 456rpx;
  margin-left: -228rpx;
  padding: 16rpx 8rpx 6rpx;
}

.is-iphone-x .panel {
  bottom: 134px;
}

.is-tab .panel {
  bottom: 50px;
}

.panel-item {
  flex: 1 1 30%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 30%;
  margin-bottom: 20rpx;
  height: 128rpx;
  background: transparent;
  font-size: 12px;
  line-height: 16px;
  color: white;
}

.panel-item::after {
  display: none;
}

.panel-item .item {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 10rpx;
  box-shadow: none;
}

.panel-item .has-badge::before {
  top: 6px;
  right: 6px;
}

.outer-space {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
}

/*
==== 面板展开
*/
.active .panel {
  opacity: 1;
  visibility: visible;
  transform: none;
}

.active .outer-space {
  width: 100%;
  height: 100%;
  z-index: 2;
}
