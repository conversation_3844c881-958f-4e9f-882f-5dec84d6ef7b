class IconController {
  constructor(base = 30, gap = 10) {
    const pages = getCurrentPages();
    const id = pages[pages.length - 1].__wxExparserNodeId__; // page 的 __wxExparserNodeId__ 唯一标志一个实例

    const instance = IconController.instanceMap[id];

    if (instance) {
      return instance;
    }

    this.id = id;
    this.base = base;
    this.gap = gap;
    this.iconMap = {};
    IconController.instanceMap[this.id] = this;
  }

  // static instanceMap = {} // 实例集合 小程序不支持这种写法

  /**
   * 设置基础位置
   * @param {Number} base 基础位置
   */
  setBase(base) {
    this.base = base;
    return this;
  }

  /**
   * 设置组件间距
   * @param {Number} gap 组件间距
   */
  setGap(gap) {
    this.gap = gap;
    return this;
  }

  /**
   * 设置图标
   * @param {Object} icon 图标组件
   * @param {Object} opts
   * @param {Number} opts.priority 优先级
   * @param {Number} opts.height 图标自身高度
   * @param {Function} opts.cb 回调函数
   */
  setIcon(icon, opts = {}) {
    const { __wxExparserNodeId__: id } = icon;

    if (this.iconMap[id]) {
      opts.priority = opts.priority || this.iconMap[id].priority || 0;
      opts.height =
        opts.height === 0 ? 0 : opts.height || this.iconMap[id].height;
      opts.cb = opts.cb || this.iconMap[id].cb;
    }

    Object.keys(this.iconMap).some((id) => {
      // 如果优先级相等，则覆盖。很奇葩，暂时这么处理了。
      if (this.iconMap[id].priority === opts.priority) {
        delete this.iconMap[id];
        return true;
      }
      return false;
    });

    this.iconMap[id] = { ctx: icon, ...opts };

    Object.keys(this.iconMap)
      .sort((a, b) => this.iconMap[b].priority - this.iconMap[a].priority) // 按优先级排序。优先级高的排前面
      .reduce((sum, id, index, arr) => {
        // 按优先级从高到低的顺序，计算高度和，调用低优先级组件的回调方法
        const { ctx, priority, height, cb } = this.iconMap[id];

        if (arr.length > 1 && priority <= opts.priority) {
          // 如果当前组件的优先级 <= 传入组件的优先级，则调用当前组件的回调方法
          if (typeof cb === 'function') {
            cb.call(ctx, sum);
          } else if (Array.isArray(cb) && typeof cb[0] === 'function') {
            cb[0].call(ctx, sum);
          }
        }

        return sum + height + (height > 0 ? this.gap : 0); // 计算高度和;
      }, this.base);

    return this;
  }

  /**
   * 设置独自一人
   * @param {Object} icon 图标组件
   * @param {Boolean} alone 是否独自一人
   */
  setAlone(icon, alone) {
    Object.keys(this.iconMap).forEach((id) => {
      const { ctx, cb } = this.iconMap[id];

      if (
        id !== icon.__wxExparserNodeId__ &&
        Array.isArray(cb) &&
        typeof cb[1] === 'function'
      ) {
        cb[1].call(ctx, alone);
      }
    });

    return this;
  }

  // 销毁实例
  destroy({ __wxExparserNodeId__: id }) {
    if (!id) {
      return;
    }

    delete this.iconMap[id]; // 删除图标

    if (Object.keys(this.iconMap).length === 0) {
      // 如果图标集合为空了，删除此集合
      delete IconController.instanceMap[this.id];
    }
  }
}

IconController.instanceMap = Object.create(null);

export default IconController;
