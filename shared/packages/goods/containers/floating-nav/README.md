# 悬浮窗

- 点击分享按钮时，触发`share`事件；

## IconController

对于其他图标组件，需要: 

- 在 `attached` 时，调用 IconController 构造方法，然后调用 `setIcon`，将组件实例 `this` 、排序优先级 `priority`、图标高度 `height`、回调函数 `cb` 传递进去，例：

```js
attached() {
  this.iconController = new IconController().setIcon(this, {
    priority: 90,
    height: 50,
    cb: [
      bottom => {
        this.setYZData({ bottom: bottom + 'px' });
      },
      goaway => {
        this.setYZData({ goaway });
      }
    ]
  });
}
```

`cb` 接收一个函数或者一个由两个函数组成的数组，函数 1 是高度变化时的回调，函数 2 是图标需要滑出屏幕时的回调。函数 1 的入参是一个 `Number` 值，代表组件需要调整到的高度位置；函数 2 的入参是一个 `Boolean` 值，代表是否需要滑出屏幕，以给悬浮窗腾出空间；

- 在组件 `detached` 时，调用 iconController 实例的 `destroy` 方法，将组件实例 `this` 传递进去，例: 

```js
detached() {
  this.iconController.destroy(this);
}
```

组件优先级整理（优先级越高，在页面的位置越靠下）: 

1. 悬浮窗优先级应是最高的，100
2. 分销员图标，90
3. 收藏有礼，80
