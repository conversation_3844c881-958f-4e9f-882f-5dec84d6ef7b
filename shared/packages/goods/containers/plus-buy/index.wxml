<van-cell-group
  wx:if="{{ plusBuy.activityId }}"
  border="{{ false }}"
  class="discount-packages-container"
  bindtap="handleClick"
>
  <van-cell
    is-link
    title-width="44px"
    value-class="cell-value__class"
  >
    <view class="van-font-12 van-c-gray-darker activity-title-desc van-ellipsis">
      <text class="activity-title-word">换购</text>
      {{ plusBuy.desc }}
    </view>
  </van-cell>
  <van-cell  value-class="cell-value__class" border="{{ false }}">
    <theme-view color="general">
      <scroll-view class="scroll-view" scroll-x="{{ true }}">
        <view
          wx:for="{{ plusBuy.goodsList }}"
          wx:key="{{ index }}"
          class="goods-item"
          data-alias="{{ item.alias }}"
        >
          <image mode="aspectFit" class="goods-item__image" src="{{ item.imageUrl }}" />
          <view wx:if="{{ item.tag }}" class="goods-item__tag">{{ item.tag }}</view>
          <view class="goods-item__price van-font-12">￥{{ item.price }}</view>
        </view>
      </scroll-view>
    </theme-view>
  </van-cell>
</van-cell-group>