import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';
import navigate from 'shared/utils/navigate';


VanxComponent({
  mapData: {
    ...mapState({
      plusBuy: (state) => state.orderActivity.plusBuy,
    }),
  },

  methods: {
    handleClick() {
      navigate.navigate({
        url: `/packages/ump/plusbuy/index?activityId=${this.data.plusBuy.activityId}`,
      });
    }
  }
});
