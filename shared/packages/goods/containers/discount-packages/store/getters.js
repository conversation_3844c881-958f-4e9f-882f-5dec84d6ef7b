import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import money from '@youzan/weapp-utils/lib/money';

export default {
  packageBuy(state) {
    const packageBuy = (state.orderActivity.packageBuy || []).map(v => ({
      ...v,
      pictureUrl: cdnImage(v.pictureUrl, '!200x0.jpg'),
      discount: `${v.hasMaxSavings ? '最多可省' : '节省'}￥${money(v.saveMoneyCn).toYuan()}`,
      priceStr: money(v.price).toYuan() + (v.planType === 1 ? '起' : '')
    }));
    return packageBuy;
  }
};

// 可配置项
// buy: true,
// show: true,
// cart: true,
// quota: true, // 是否显示限购
// text:
// isUmp: false,
