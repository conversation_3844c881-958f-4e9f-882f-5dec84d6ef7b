/**
 * 优惠套餐推荐区域
 */
import { mapState, mapGetters } from '@youzan/vanx';
import navigate from 'shared/utils/navigate';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  mapData: {
    ...mapState(['goods']),
    ...mapGetters(['packageBuy'])
  },

  methods: {
    handleDiscountPackagesClick() {
      const goods = this.data.goods || {};
      navigate.navigate({
        url: `/packages/ump/discount-packages/index?goodsId=${goods.id}`
      });
    }
  }
});
