.scroll-view {
  padding-bottom: 10px;
  height: 90px;
  display: flex;
  flex-flow: nowrap row;
  white-space: nowrap;
}

.goods-item {
  display: inline-block;
  width: 252px;
  margin-right: 32px;
  position: relative;
  line-height: 16px;

  &-content {
    margin-top: 12px;
    float: right;
    width: calc(100% - 100px);
  }

  &-content--top {
    margin-top: 22px;
  }

  &__image {
    margin-right: 8px;
    width: 90px;
    height: 90px;
  }

  &__name {
    font-size: 14px;
    font-family: PingFangSC-Medium;
    color: #323233;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__price {
    margin: 10px 0 8px;
    font-size: 14px;
  }

  &__save {
    color: #969799;
    font-size: 12px;
  }
}

.goods-item--long {
  width: 100%;
  margin-right: 0;
}
