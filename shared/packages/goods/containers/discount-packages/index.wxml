<van-cell-group
  wx:if="{{ packageBuy.length }}"
  border="{{ false }}"
  class="discount-packages-container"
  bindtap="handleDiscountPackagesClick"
>
  <van-cell
    is-link="{{ packageBuy.length > 1 }}"
    title="优惠套餐" 
    class="align-right"
    title-class="cell-title__class"
  >
    <view
      wx:if="{{ packageBuy.length > 1 }}" 
      class="van-font-12 van-c-gray-darker"
    >
      查看更多
    </view>
  </van-cell>

  <!-- 套餐信息 -->
  <van-cell
    value-class="cell-value__class"
    border="{{ false }}"
  >
    <theme-view color="general">
      <scroll-view
        class="scroll-view"
        scroll-x
      >
        <view
          wx:for="{{ packageBuy }}"
          wx:key="{{ index }}"
          class="goods-item {{ packageBuy.length === 1 ? 'goods-item--long' : ''}}"
        >
          <image
            mode="aspectFit"
            class="goods-item__image"
            src="{{ item.pictureUrl }}"
          />
          <view class="goods-item-content {{ item.planType === 1 || !item.showPreference ? 'goods-item-content--top' : '' }}">
            <view class="goods-item__name">{{ item.title }}</view>
            <view class="goods-item__price van-font-12">套餐价：￥{{ item.priceStr }}</view>
            <view
              wx:if="{{ item.planType !== 1 && item.showPreference }}"
              class="goods-item__save"
            >
              {{ item.discount }}
            </view>
          </view>
        </view>
      </scroll-view>
    </theme-view>
  </van-cell>
</van-cell-group>