// import { mapState } from '@youzan/vanx';
// import get from '@youzan/weapp-utils/lib/get';
import { GoodsComponent } from '@goods/common/vanx-component';

GoodsComponent({
  state: ['salesman'],

  // 生成小程序二维码自定义参数
  getters: ['makeCodeCustomData', 'cubeGoodsInfo'],

  data: {
    cubeConfig: [['share', 'code', 'promote', 'salesman', 'material', 'zoom']],
  },

  methods: {
    handleShareCard() {
      this.triggerEvent('share');
    },
  },
});
