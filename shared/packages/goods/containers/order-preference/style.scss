@import '~shared/common/css/_mixins.scss';

.order-preference__container {
  
  .order-preference__item {
    display: flex;
    font-size: 12px;
    color: #111;
    overflow: hidden;
    padding: 10px 10px 0 0;

    &:first-child {
      padding-top: 0;
    }
    
    zan-tag {
      display: block;
      width: 32px;
      margin-right: 10px;
      white-space: nowrap;
    }
  }
}

.order-preference__popup {
  margin: 15px;
  font-size: 12px;
  color: #111;

  .order-preference__items {
    position: absolute;
    left: 0;
  }

  .order-preference__item {
    position: relative;
    display: flex;
    line-height: 24px;
    padding: 15px 15px 15px 1px;
    width: calc(100vw - 15px);
    box-sizing: border-box;

    &:first-child {
      padding-top: 0;
    }

    .item-view {
      margin: 1px 0 0 10px;
      color: #111;
      font-size: 12px;
    }

    & .link-item {
      margin-right: 15px;

      &::after {
        @include customer-arrow;
        position: absolute;
        top: 28px;
        right: 15px;
        left: auto;
      }
    }
  }
}

.postage-free-inner::after {
  left: 38px;
}

.order-preference__tag {
  margin-right: 10px;
}

.goods-detail__tag--plain {
  min-width: 32px;
  box-sizing: border-box;
  white-space: nowrap;
}

.post-item__tag.invisiable {
  visibility: hidden;
}

.inline-view {
  display: inline;
}

.color-38f {
  color: #3388FF;
}
