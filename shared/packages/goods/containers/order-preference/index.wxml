<view
  wx:if="{{ promotionLength }}"
  class="container"
>
  <van-cell
    is-link
    border="{{ false }}"
    title="促销"
    title-width="42px" 
    class="order-preference__container"
    value-class="cell-value__class"  
    title-class="cell-title__class"
    bind:tap="showPopup"
  >
    <view
      wx:for="{{ orderPromotion }}"
      wx:key="*this"
      class="order-preference__item"
    >
      <van-tag
        plain
        type="danger"
        color="{{ themeGeneral }}"
        class="order-preference__tag"
        custom-class="goods-detail__tag--plain"
      >{{ item.title }}</van-tag>
      <view wx:if="{{ index !== 'present' }}" class="van-ellipsis">{{ item.desc }}</view>
      <view wx:else class="van-ellipsis">
        <template is="present-list-block" data="{{ present: item.present }}"></template>
      </view>
    </view>
  </van-cell>

  <goods-popup
    show="{{ isPopupShow }}"
    bind:tap:btn="closePopup"
    scroll-class="order-preference__popup"
  >
    <view class="order-preference__items">
      <view
        wx:for="{{ orderPromotion }}"
        wx:for-index="activityType"
        wx:key="*this"
        class="order-preference__item van-hairline--bottom"
        bind:tap="handleItemClick"
        data-type="{{ activityType }}"
      >
        <van-tag
          plain
          type="danger"
          color="{{ themeGeneral }}"
          custom-class="goods-detail__tag--plain"
        >{{ item.title }}</van-tag>
        <template wx:if="{{ activityType === 'present' }}" is="present-block" data="{{ present: item.present, startAt: item.startAt, endAt: item.endAt }}"></template>
        <template wx:elif="{{ activityType === 'cashBack' }}" is="cashback-block" data="{{ cashBack: item, startAt: item.startAt, endAt: item.endAt }}"></template>
        <view wx:else class="item-view {{ item.isLink ? 'link-item' : '' }}">
          <view>{{ item.desc }}</view>
          <view wx:if="{{ item.startAt && item.endAt }}" class="van-c-gray-darker">{{ item.startAt }} - {{ item.endAt }}</view>
        </view>
      </view>
    </view>
  </goods-popup>
</view>

<!-- 返现模块特殊展示 -->
<template name="cashback-block">
  <view class="item-view">
    <view wx:if="{{ cashBack.isTuan }}">
      {{ cashBack.startAt }} 至 {{ cashBack.endAt }} 购买该团购商品，在享受团购价的同时，根据售出件数可获得累计返现:
      <view wx:for="{{ cashBack.rules }}" wx:key="min">
        售出达{{ item.min }}件，可获得返现{{ item.value / 100 }}元{{ index + 1 === cashBack.rules.length ? '。' : ';' }}
      </view>
    </view>
    <view wx:else>
      你在本店支付成功的前{{ cashBack.limit }}笔订单，{{ cashBack.cashbackMethod === 'random' ? '最高' : '' }}可获得订单金额
      <text class="van-c-red">{{ cashBack.cashbackEnd }}%</text>
      的返现
    </view>
  </view>
</template>

<!-- 赠品模块特殊展示 -->
<template name="present-block">
  <view class="item-view">
    <view>
      <template is="present-list-block" data="{{ present }}"></template>
    </view>
    <view class="van-c-gray-darker">{{ startAt }}-{{ endAt }}</view>
  </view>
</template>

<template name="present-list-block">
  <block
    wx:for="{{ present }}"
    wx:for-item="presentLevelItem"
    wx:for-index="presentLevelIndex"
    wx:key="index"
  >
    {{ presentLevelItem.presentPrefix }}<!--
    --><block
      wx:if="{{ presentLevelItem.presentList.length > 0 }}"
      wx:for="{{ presentLevelItem.presentList }}"
      wx:for-item="presentItem"
      wx:for-index="presentIndex"
      wx:key="*this"
      class="inline-view"
    >
      送
      <navigator
        hover-class="none"
        class="inline-view color-38f"
        url="/pages/goods/detail/index?alias={{ presentItem.detail.alias }}"
      >
        {{ presentItem.detail.title }}
      </navigator>
    </block>；
  </block>
</template>
