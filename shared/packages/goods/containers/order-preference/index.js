import theme from 'shared/common/components/theme-view/theme';
import openWebView from 'shared/utils/open-web-view';
import getApp from 'shared/utils/get-safe-app';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState } from '@youzan/vanx';

VanxComponent({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  mapData: {
    ...mapState([
      'orderPromotion',
      'goods'
    ]),
    ...mapState({
      promotionLength: (state) => Object.keys(state.orderPromotion || {}).length
    })
  },

  data: {
    themeGeneral: '',
    isPopupShow: false,
  },

  ready() {
    theme.getThemeColor('general')
      .then(color => {
        this.setYZData({
          themeGeneral: color
        });
      });
  },

  methods: {
    showPopup() {
      this.setYZData({
        isPopupShow: true
      });
    },

    closePopup() {
      this.setYZData({
        isPopupShow: false
      });
    },

    handleItemClick({ currentTarget = {} }) {
      const { dataset = {} } = currentTarget;
      if (dataset.type === 'postage') {
        this._goToExpressDetail();
      }
    },

    _goToExpressDetail() {
      const app = getApp();
      openWebView('/wscshop/showcase/postrules', {
        title: '包邮规则',
        query: {
          kdtId: app.getKdtId() || 0,
          goodsId: this.data.goods.id,
          offlineId: app.getOfflineId() || 0
        }
      });
    }
  }
});
