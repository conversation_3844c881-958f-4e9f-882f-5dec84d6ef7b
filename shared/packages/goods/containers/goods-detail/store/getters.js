import cdnImage from '@youzan/weapp-utils/lib/cdn-image';

export default {
  // 商品详情跳转链接
  fastGoodsDetailUrl(state, getters) {
    const { wap } = state.url;
    const { alias } = getters;

    return `${wap}/showcase/goodsfast?alias=${alias}&detail_type=goods`;
  },

  // 本店成交跳转链接
  fastTradeRecordsUrl(state, getters) {
    const { wap } = state.url;
    const { alias } = getters;

    return `${wap}/showcase/goodsfast?alias=${alias}&detail_type=sales`;
  },

  // 商品详情展示类目
  goodsDetailTabs(state, getters) {
    const { showBuyRecordListStyle } = getters;
    const { goodsSoldNum, fastGoodsDetailUrl, fastTradeRecordsUrl } = getters;
    const tabs = [];

    // todo 拼团会加活动 参数
    tabs.push({
      type: 'goods',
      desc: getters.isNormalTplStyle ? '商品详情' : '查看商品详情',
      url: fastGoodsDetailUrl
    });

    if (goodsSoldNum > 0 && showBuyRecordListStyle) {
      tabs.push({
        type: 'sales',
        desc: '本店成交',
        url: fastTradeRecordsUrl
      });
    }

    return tabs;
  },

  // 当前激活的 tab 索引，存在通过url设置当前激活tab的场景
  activeDetailTabIndex(state, getters) {
    const { defaultType } = state.goodsDetail;
    const { goodsDetailTabs } = getters;
    let index = 0;

    for (let i = 0, l = goodsDetailTabs.length; i < l; i++) {
      if (goodsDetailTabs[i].type === defaultType) {
        index = i;
        break;
      }
    }

    return index;
  },
  // 是否是普通版商品详情
  isNormalTplStyle() {
    // 小程序内，不支持商品详情极速版
    return true;
    // return state.goodsDetail.tplStyle === 0;
  },

  shopName(state) {
    const { name, hideStore } = state.multistore;
    return hideStore ? state.shop.name : name || state.shop.name;
  },

  shopLogo({ shop }) {
    const formatedImage = shop.image || shop.logo;
    return /\?/.test(formatedImage) ? formatedImage : cdnImage(formatedImage, '!100x100.png');
  },

  // 店铺链接
  shopLink(state) {
    const { id } = state.multistore;
    let { url = '' } = state.shop;

    if (id) {
      const symbol = ~url.indexOf('?') ? '&' : '?';
      url += `${symbol}oid=${id}`;
    }

    return url;
  },
};
