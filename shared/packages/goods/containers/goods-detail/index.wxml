<import src="./components/haitao/index.wxml" />

<van-tabs
  wx:if="{{ goodsDetailTabs.length > 1 }}"
  color="{{ mainColor }}"
  z-index="{{ 1 }}"
  border="{{ false }}"
  active="{{ selectedId }}"
  class="goods-detail__tab"
  bind:click="handleTabChange"
>
  <van-tab title="{{ item.desc }}" wx:for="{{ goodsDetailTabs }}" wx:key="{{ item.type }}" />
</van-tabs>
<view wx:else class="goods-detail__tab goods-detail__title van-hairline--bottom">商品详情</view>

<view
  hidden="{{ selectedId !== 0 }}"
  class="goods-detail {{ goodsDetailTabs.length ? 'goods-detail--in-tab' : '' }}"
>
  <showcase-container
    extra="{{ showcaseExtra }}"
    shop-name="{{ shopName }}"
    shop-logo="{{ shopLogo }}"
    unique-key="{{ themeFeature.uniqueKey }}"
    components-length="{{ themeFeature.componentsLength }}"
    page-lifetimes="{{ ['onReachBottom', 'onPullDownRefresh'] }}"
    bind:buy="showcaseHandleGoodsBuy"
  ></showcase-container>
  <template wx:if="{{ isHaitao }}" is="haitao-inform"></template>
  <goods-price-intro/>
</view>

<goods-sale-record hidden="{{ selectedId !== 1 }}" goods-alias="{{ goods.alias }}"/>

<feature-sku
  wx:if="{{ featureSkuData }}"
  feature-sku-data="{{ featureSkuData }}"
  bind:close="handleSkuHide"
/>
