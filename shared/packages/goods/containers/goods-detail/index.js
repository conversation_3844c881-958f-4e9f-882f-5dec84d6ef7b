import get from '@youzan/weapp-utils/lib/get';
import Event from '@youzan/weapp-utils/lib/event';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import theme from 'shared/common/components/theme-view/theme';
import FeatureLoadControl from 'shared/components/showcase-options/feature-load/index';
import { featureSkuUtilsPromise } from 'shared/utils/async-base-sku';
import { mapState, mapGetters } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapState({
      goods: (state) => state.goods || {},
      isHaitao: (state) => state.haitao.isHaitao,
      themeFeature: (state) => {
        const themeFeature = get(state, 'goodsDetail.themeFeature', {
          uniqueKey: '',
          componentsLength: 0,
        });
        const components = get(state, 'goodsDetail.showcaseComponents', []);
        const uniqueAlias = get(state, 'pageParams.uniqueAlias', '');

        // 确保该商品 只绑定一个 event key
        const reg = new RegExp('feature-load:.+' + uniqueAlias);
        Object.keys(Event._events).forEach((key) => {
          if (reg.test(key)) Event.off(key);
        });

        FeatureLoadControl.setShowcaseComponents(
          components,
          false,
          false,
          themeFeature.uniqueKey
        );
        return themeFeature;
      },
    }),
    ...mapState('/', {
      showcaseExtra(state = {}) {
        const app = getApp();
        const { config = {}, token = {}, shop = {} } = state;
        const { chainStoreInfo = {} } = app.getShopInfoSync();
        const { isMultiOnlineShop } = chainStoreInfo;
        const lsKdtId = isMultiOnlineShop ? app.getHQKdtId() : app.getKdtId();

        return {
          appId: config.appId,
          kdtId: shop.kdtId,
          lsKdtId,
          buyerId: token.userId,
          offlineId: shop.offlineId || '',
        };
      },
    }),
    ...mapGetters([
      'goodsDetailTabs',
      // 店铺基础信息
      'shopLogo',
      'shopName',
    ]),
  },

  data: {
    mainColor: '',
    selectedId: 0,
    featureSkuData: '',
  },

  attached() {
    theme.getThemeColor('main-bg').then((color) => {
      this.setYZData({
        themeMainColor: color,
      });
    });
  },

  detached() {
    const { themeFeature: { uniqueKey = '' } = {} } = this.data;
    FeatureLoadControl.clearShowcaseComponents(uniqueKey);
  },

  methods: {
    handleTabChange({ detail }) {
      this.setYZData({
        selectedId: detail.index,
      });
    },

    showcaseHandleGoodsBuy({ detail }) {
      const presale = detail.preSale || detail.pre_sale;
      const alias = detail.alias;

      if (+presale === 1) {
        wx.navigateTo({
          url: `/pages/goods/detail/index?alias=${alias}`,
        });
        return;
      }

      featureSkuUtilsPromise()
        .then(({ getSkuData }) => getSkuData(alias))
        .then((skuData) => {
          this.setYZData({
            featureSkuData: skuData,
          });
        })
        .catch((err) => {
          console.log(err);
        });
    },

    handleSkuHide() {
      this.setYZData({
        'featureSkuData.showGoodsSku': false,
      });
    },
  },
});
