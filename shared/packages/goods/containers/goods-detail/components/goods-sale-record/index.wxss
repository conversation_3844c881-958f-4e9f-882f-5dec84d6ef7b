/* 成交记录 */
@import "shared/common/css/helper/index.wxss";

.goods-sale-record {
  font-size: 12px;
  background-color: #fff;
  overflow: hidden;
}

.goods-sale-record__header {
  display: flex;
  padding: 0 10px;
  line-height: 33px;
  background-color: #f2f2f2;
}

.goods-sale-record__body {
  padding-left: 10px;
}

.goods-sale-record__item {
  display: flex;
  padding: 10px 10px 10px 0;
}

.goods-sale-record__item:last-child::after {
  border-bottom: 0 none;
}

.goods-sale-record__col-1,
.goods-sale-record__col-2,
.goods-sale-record__col-3 {
  flex: 1;
}

.goods-sale-record__col-2,
.goods-sale-record__col-3 {
  text-align: center;
}

.goods-sale-record__header .goods-sale-record__col-1 {
  text-indent: 12px;
}

.goods-sale-record__item .goods-sale-record__col-1 {
  color: #666;
  text-indent: 5px;
}

.goods-sale-record__item .goods-sale-record__col-2 {
  color: #999;
}

.goods-sale-record__item .goods-sale-record__col-3 {
  color: #666;
}

.loadmore {
  display: block;
  margin: 15px;
}

.loadmore-nodata {
  margin-top: 40px;
}

.goods-sale-record__view-more {
  padding: 10px 0;
  font-size: 14px;
  color: #666;
  text-align: center;
}
