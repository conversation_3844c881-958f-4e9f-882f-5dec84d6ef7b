import Toast from '@vant/weapp/dist/toast/toast';
import WscComponent from 'shared/common/base/wsc-component/index';
import { node as request } from 'shared/utils/request';

WscComponent({
  properties: {
    goodsAlias: {
      type: String,
      observer() {
        this.fetchSaleRecord(1);
      }
    },
    hidden: Boolean
  },

  data: {
    saleRecord: {
      list: [],
      finished: false
    },
    loadMore: {
      show: false,
      type: 'loading',
      text: ''
    }
  },

  methods: {
    fetchSaleRecord(page = 1) {
      this.setYZData({
        loadMore: {
          show: true,
          type: 'loading',
          text: ''
        }
      });
      request({
        path: 'v2/trade/order/orderitemlist.json',
        data: {
          page,
          alias: this.data.goodsAlias,
          perpage: 10
        }
      }).then((data) => {
        const list = [
          ...this.data.saleRecord.list,
          ...data.list
        ];
        const finished = list.length >= data.total;
        this.setYZData({
          saleRecord: {
            list,
            finished,
            nextPage: page + 1
          },
          loadMore: {
            show: list.length === 0 && finished,
            type: 'text',
            text: '暂无成交记录'
          }
        });
      }).catch(() => {
        this.setYZData({
          loadMore: {
            show: true,
            type: 'text',
            text: '暂无成交记录'
          }
        });

        Toast({
          message: '获取成交记录失败',
          context: this
        });
      });
    },

    handleFetchMoreSaleRecord() {
      this.fetchSaleRecord(this.data.saleRecord.nextPage);
    }
  }
});
