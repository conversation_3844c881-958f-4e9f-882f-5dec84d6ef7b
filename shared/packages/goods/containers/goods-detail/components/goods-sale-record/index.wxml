<view hidden="{{ hidden }}" class="goods-sale-record">
  <view wx:if="{{  saleRecord.list.length }}" class="goods-sale-record__header van-hairline--bottom">
    <text class="goods-sale-record__col-1">买家</text>
    <text class="goods-sale-record__col-2 center">成交时间</text>
    <text class="goods-sale-record__col-3 center">数量</text>
  </view>
  <view class="goods-sale-record__body">
    <view wx:for="{{ saleRecord.list }}" wx:key="{{ index }}" class="goods-sale-record__item van-hairline--bottom">
      <text class="goods-sale-record__col-1">{{ item.nickname }}</text>
      <text class="goods-sale-record__col-2">{{ item.update_time }}</text>
      <text class="goods-sale-record__col-3">{{ item.item_num }}</text>
    </view>
  </view>
  <view wx:if="{{ !saleRecord.finished && !saleRecord.loading && !saleRecord.nodata }}" class="goods-sale-record__view-more van-hairline--top" bind:tap="handleFetchMoreSaleRecord">
    查看更多
  </view>
  <!-- loadmore -->
  <loadmore wx:if="{{ loadMore.show }}" type="{{ loadMore.type }}" text="{{ loadMore.text }}" />
</view>

<van-toast id="van-toast" />