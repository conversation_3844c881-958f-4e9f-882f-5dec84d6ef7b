  <view class="dr-bar__container">
    <view class="sideline-left" />
    <view bindtap="onDrbarClick" class="content">
      价格说明
      <van-icon style="font-size: 8px;color: #999;top:1px;position:relative;" name="arrow"></van-icon>
    </view>
    <view class="sideline-right" />
  </view>

  <goods-popup show="{{ show }}" title="价格说明" bind:tap:btn="closePopup">
    <view class="price-intro__container">
      <view>
        <text>划线价格：</text>
        划线的价格可能是商品的专柜价、吊牌价、正品零售价、指导价、曾经展示过的销售价等，仅供你参考。
      </view>
      <view>
        <text>未划线价格：</text>
        未划线的价格是商品的销售标价，具体的成交价格可能因会员使用优惠券、积分等发生变化，最终以订单结算价格为准。
      </view>
      <view>*此说明仅当出现价格比较时有效。若这件商品针对划线价格进行了特殊说明，以特殊说明为准。</view>
    </view>
  </goods-popup>