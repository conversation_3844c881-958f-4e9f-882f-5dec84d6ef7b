.dr-bar__container {
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 15px;
  background: #fff;
}

.dr-bar__container .sideline-left,
.dr-bar__container .sideline-right {
  flex: 1;
  height: 1px;
  vertical-align: middle;
  background: #e5e5e5;
  transform: scaleY(.5);
}

.dr-bar__container .content {
  display: inline-block;
  font-size: 10px;
  margin: 0 10px;
  opacity: 0.8;
  color: #333333;
  line-height: 17px;
  text-align: left;
}

.dr-bar__container .content.radius {
  border: 1px solid #e5e5e5;
  border-radius: 29px;
  padding: 0 10px;
}

.dr-bar__container .content.radius .zan-icon {
  position: relative;
  color: #999999;
  font-size: 8px;
  line-height: 18px;
  margin-left: 5px;
}


.price-intro__container view {
  margin-bottom: 10px;
}

.price-intro__container text {
  font-size: 12px;
  color: #333;
}

.price-intro__container {
  font-size: 12px;
  color: #999999;
  line-height: 14px;
  text-align: left;
  padding: 20px 15px 10px;
}