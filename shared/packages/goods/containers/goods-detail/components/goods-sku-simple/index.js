import WscComponent from 'shared/common/base/wsc-component/index';
import getSafeApp from 'shared/utils/get-safe-app';
import { fetchGoodsSkuData } from './fetch';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

WscComponent({
  properties: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        if (!newVal) {
          return;
        }

        this.fetchAndShowSku();
      },
    },
    alias: String,
  },

  data: {
    showcaseBuyData: {},
  },

  methods: {
    fetchAndShowSku() {
      if (!this.data.alias) {
        return;
      }

      wx.showLoading();
      fetchGoodsSkuData(this.data.alias)
        .catch((res) => {
          wx.hideLoading();
          wx.navigateTo({
            url: `/pages/goods/detail/index?alias=${this.data.alias}`,
          });
          this._closeSku(res);
          return Promise.reject();
        })
        .then((res) => {
          wx.hideLoading();
          console.log(res);
          this.setYZData({
            showcaseBuyData: {
              ...res,
              show: true,
            },
          });
        });
    },

    handleAddCart({ detail }) {
      const { goodsData, commonData, cartMessages } = this._getSkuData(detail);

      skuTradeUtilsPromise()
        .then(({ addToCart }) => {
          return addToCart({
            ...goodsData,
            ...commonData,
            cartMessages,
          });
        })
        .then(() => {
          wx.showToast({
            title: '添加购物车成功',
            icon: 'none',
          });
          this._closeSku({ msg: '添加购物车成功' });
          // 刷新购物车列表
          app.trigger('component:sku:cart', {
            type: 'add',
          });
        })
        .catch((err = {}) => {
          wx.showToast({
            title: err.msg || '加入购物车失败',
            icon: 'none',
            duration: 2000,
          });
        });
    },

    handleBuy({ detail }) {
      const { goodsData, commonData, messages } = this._getSkuData(detail);
      skuTradeUtilsPromise().then(({ goToBuySingle }) => {
        goToBuySingle({
          ...goodsData,
          ...commonData,
          messages,
        });
      });
    },

    handleSkuClose() {
      this._closeSku();
    },

    _closeSku(res = {}) {
      this.setYZData({
        'showcaseBuyData.show': false,
      });
      this.triggerEvent('hide', res);
    },

    _getSkuData(detail) {
      const { showcaseBuyData } = this.data;
      const {
        skuGoodsDetail: { id },
      } = showcaseBuyData;

      const app = getSafeApp();
      const goodsData = {
        goodsId: id,
        num: detail.num,
        skuId: detail.skuId,
        price: detail.price,
        qr: '',
        tpps: '',
      };
      const commonData = {
        kdtId: app.getKdtId(),
        postage: 0,
        activityAlias: '',
        activityId: 0,
        activityType: '',
        useWxpay: 0,
        orderFrom: '',
      };

      const result = {
        goodsData,
        commonData,
        messages: detail.messages,
        cartMessages: detail.cartMessages,
      };

      return result;
    },
  },
});
