import { node as request } from 'shared/utils/request';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { formatSkuTree } from 'shared/common/components/base-sku/common/sku-format-tree';

export function fetchGoodsSkuData(alias) {
  return request({
    path: '/wscshop/goods/detail.json',
    query: {
      alias,
    },
  }).then((res) => {
    if (res.type === 'ecard') {
      return Promise.reject();
    }
    console.log(res);

    const goodsData = res.goods || {};
    const { goods, shop } = goodsData;
    let { sku } = goodsData;
    sku = formatSkuTree(sku);
    const shopConfig = shop.config;
    const startSaleNum = +goods.startSaleNum || 1;
    const quotaTexts = startSaleNum > 1 ? [`${startSaleNum}件起售`] : [];
    const quota = goods.quota ? Math.max(goods.quota, 1) : 0;
    const picture = goods.pictures[0] || {};
    if (goods.buyWay === 0) {
      // 外链商品限购0件
      sku.stockNum = 0;
    }
    let periodText = '';
    switch (goods.quotaCycle) {
      case 1:
        periodText = '每天';
        break;
      case 2:
        periodText = '每周';
        break;
      case 3:
        periodText = '每月';
        break;
      default:
        periodText = '';
    }

    if (quota > 0) {
      quotaTexts.push(`每人${periodText}限购${quota}件`);
    }

    return {
      sku,
      skuGoodsDetail: {
        id: goods.id,
        alias: goods.alias,
        title: goods.title,
        picture: cdnImage(picture.url || '', '!300x300.jpg'),
        isVirtual: !!goods.virtual,
      },
      quota,
      quotaUsed: goods.quotaUsed,
      quotaText: quotaTexts.join('，'),
      skuHideStock: sku.hideStock,
      skuShowAddCartBtn: !goods.virtual && !shopConfig.hideShoppingCart,
      startSaleNum,
    };
  });
}
