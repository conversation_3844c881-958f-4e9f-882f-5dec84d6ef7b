import { mapState, mapGetters } from '@youzan/vanx';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { node as request } from 'shared/utils/request';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import get from '@youzan/weapp-utils/lib/get';

VanxComponent({
  mapData: {
    ...mapGetters(['showBuyerShows']),
    ...mapState(['goods']),
  },

  data: {
    showCellTitle: '买家秀',
    showCellValue: '查看全部',
    buyerShowListUrl: '',
    showList: []
  },

  ready() {
    const showBuyerShows = get(this.data, 'showBuyerShows');
    const { goods = {} } = this.data;
    if (showBuyerShows) {
      this.getBuyerShows(goods.alias);
    }
  },

  methods: {
    getBuyerShows(alias) {
      request({
        path: '/wscgoods/buyer-show-info.json',
        data: { alias },
        config: {
          cache: true,
          expire: 30,
        }
      }).then(data => {
        const { list, count } = data;
        const showList = list.map(item => {
          item.image = cdnImage(item.picturesList[0], '!300x0.jpg');
          return item;
        });
        this.setYZData({
          showList,
          showCellTitle: '买家秀' + (count > 0 ? `(${count})` : ''),
          buyerShowListUrl: `/packages/shop/buyers-show/list/index?alias=${alias}`
        });
      });
    }
  }
});

