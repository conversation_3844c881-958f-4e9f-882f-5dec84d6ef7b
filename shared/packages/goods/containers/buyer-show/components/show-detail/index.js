import navigate from 'shared/utils/navigate';

Component({
  properties: {
    showList: Array,
    alias: String,
  },

  methods: {
    showDetail({ target = {} }) {
      const { index } = target.dataset || {};
      const { showList = [], alias } = this.data;
      const { alias: evaluation<PERSON><PERSON><PERSON> } = showList[index];
      navigate.navigate({
        url: `/packages/shop/buyers-show/list/index?alias=${alias}&evaluation_alias=${evaluationAlias}`
      });
    }
  }
});
