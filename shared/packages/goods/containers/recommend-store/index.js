import Toast from '@vant/weapp/dist/toast/toast';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import { node } from 'shared/utils/request';
import { tryLocation } from 'shared/utils/lbs/index';
import getApp from 'shared/utils/get-safe-app';

const app = getApp();

VanxComponent({
  mapData: {
    ...mapGetters([
      'isMultiStoreSoldout',
    ]),
    ...mapState('/', {
      soldOutRecommend: (state) => state.shop.soldOutRecommend,
      offlineId: (state) => state.shop.offlineId,
    }),
    ...mapState({
      goodsId: (state) => state.goods.id,
    }),
  },

  data: {
    stores: [],
    user: {
      lat: '',
      lng: ''
    }
  },

  methods: {
    /**
     * 获取推荐网点列表
     */
    fetchRecommendStores() {
      const { offlineId, goodsId } = this.data;
      if (!offlineId) {
        return;
      }
      node({
        path: '/wscump/multistore/listGoodsSalableStore.json',
        query: {
          goodsId,
          currentStoreId: offlineId,
          ...this.data.user
        }
      }).then(res => {
        console.log('[fetchRecommendStores]', res);
        res = res.map((item) => {
          let distance = item.distance;
          distance = distance || 0;
          if (distance > 10000) {
            distance = '> 10km';
          } else if (distance > 1000) {
            distance = (distance / 1000).toFixed(2) + 'km';
          } else {
            distance = distance.toFixed(2) + 'm';
          }
          return Object.assign(item, {
            distance
          });
        });
        this.setYZData({ stores: res });
      }).catch(err => {
        this.setYZData({
          stores: []
        });
        Toast(err.msg || '获取推荐网点失败');
      });
    },

    startFetchRecommendStores() {
      const { isMultiStoreSoldout, soldOutRecommend } = this.data;
      if (!isMultiStoreSoldout || !soldOutRecommend) {
        return;
      }
      tryLocation(({ lng, lat }) => {
        this.setYZData({
          user: {
            lng,
            lat
          }
        });
        this.fetchRecommendStores();
      });
    },

    /**
     * 跳到别的网点
     */
    redirectToStore(e) {
      const { storeId, index } = e.target.dataset;
      // 可能没点击到某个网点
      if (!storeId) return;

      // 切换到选择的网点
      const store = this.data.stores[index];
      // this.triggerEvent('switch-store', {
      //   offlineId: store.id, store
      // });
      app.setOfflineId(store.id);
      this.setYZData({ stores: [] });
    }
  },

  ready() {
    this.startFetchRecommendStores();
  }
});
