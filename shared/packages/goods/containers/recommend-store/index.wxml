<van-toast id="van-toast" />

<view class="store-recommend__container" wx:if="{{ isMultiStoreSoldout && soldOutRecommend && stores.length }}">
  <view class="store-recommend__tip">
    <view class="van-font-12">该门店商品已售罄，你可以去其他门店购买</view>
  </view>
  <scroll-view bindtap="redirectToStore" scroll-x class="store-recommend__swiper">
    <view class="store-recommend__item" data-index="{{index}}" data-store-id="{{store.id}}" wx:for="{{stores}}" wx:index="index" wx:for-item="store" wx:key="*this">
      <view class="van-font-12 van-c-gray-darker van-ellipsis">{{store.name}}</view>
      <view wx:if="{{store.distance}}" class="van-font-12 van-c-gray" style="margin-top: 15px">
        <view class="zan-icon-location zan-icon"></view>
        {{store.distance}}
      </view>
    </view>
  </scroll-view>
</view>