@import "shared/common/css/helper/index.wxss";

.store-recommend__container {
  padding: 0 15px 10px;
  margin-top: 10px;
  box-sizing: border-box;
  background: #fff;
}

.store-recommend__tip {
  font-size: 12px;
  line-height: 32px;
  color: #111;
}

.store-recommend__swiper {
  height: 80px;
  background: #fff;
  display: flex;
  flex-flow: nowrap row;
  white-space: nowrap;
}

.store-recommend__item {
  width: 120px;
  height: 80px;
  background: #F8F8F8;
  margin-right: 10px;
  overflow:hidden;
  text-align: center;
  display:inline-block;
  padding: 12px 15px;
  box-sizing: border-box;
}

.store-recommend__item:last-child {
  margin-right: 0;
}

.store-recommend__item > view {
  pointer-events: none;
}