<base-sku
  goods="{{ skuGoodsDetail }}"
  is-virtual-ticket="{{ skuGoodsDetail.isVirtualTicket }}"
  virtual-ticket="{{ virtualTicket }}"
  price-calendar-data="{{ priceCalendarData }}"
  sku="{{ skuData }}"
  initial-sku="{{ initialSku }}"
  show="{{ skuPopShow }}"
  quota="{{ quota }}"
  quota-text="{{ quotaText }}"
  quota-cycle="{{ quotaCycle }}"
  quota-used="{{ quotaUsed }}"
  start-sale-num="{{ startSaleNum }}"
  show-buy-btn="{{ skuShowBuyBtn }}"
  show-add-cart-btn="{{ skuShowAddCartBtn }}"
  hide-stock="{{ skuData.hideStock }}"
  extra-data="{{ skuExtraData }}"
  buy-text="{{ skuBuyBtnText }}"
  cart-text="加入购物车"
  theme-class="{{ themeClass }}"
  properties="{{ properties }}"
  bind:stepper-change="stepperChange"
  bind:add-cart="handleAddCart"
  bind:buy="handleBuy"
  bind:sku-selected="handleSkuSelect"
  bind:sku-prop-selected="handlePropSelect"
  bind:sku-close="handleSkuClose"
  reset-stepper-on-hide
  generic:sku-header-price="sku-header-price"
>
  <view
    wx:if="{{ showExtraGroup }}"
    slot="extra-sku-group"
    class="van-sku-group-container"
  >
    <!-- 阶梯拼团-->
    <ladder-groupon-extra wx:if="{{ showladderGrouponExtra }}" />
    <sku-extra-periodbuy wx:if="{{ isPeriodbuy }}" />
    <installment-sku wx:if="{{ showInstallmentSku }}" />
  </view>
</base-sku>
