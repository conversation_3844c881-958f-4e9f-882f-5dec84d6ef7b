/* eslint-disable no-continue */
import get from '@youzan/weapp-utils/lib/get';
import money from '@youzan/weapp-utils/lib/money';
import cdnImage from '@youzan/weapp-utils/lib/cdn-image';
import { SKU_SCENE } from 'shared/packages/goods/common/constant';
import { everyValue } from '../../../store/utils/helpers/object';
import { getQuotaText } from 'shared/common/components/base-sku/common/quota-utils';

export default {

  // sku中，是否展示加入购物车按钮
  skuShowAddCartBtn(state, getters) {
    // 虚拟商品不支持购物车
    const isVirtual = get(state, 'goods.isVirtual', 0);
    const hideShoppingCart = get(state, 'shopConfig.hideShoppingCart', false);
    const { skuConfig = {} } = state;
    const { isMultiBtn } = skuConfig;

    if (isVirtual || getters.isPeriodbuy || getters.isSeckill || hideShoppingCart) {
      return false;
    }

    // 有两个按钮或者只显示加入购物车
    return isMultiBtn;
  },

  skuShowBuyBtn(state, getters) {
    const showBuyBtn = get(state, 'display.showBuyBtn', false);
    if (!showBuyBtn && !getters.skuShowAddCartBtn) {
      return true;
    }
    return showBuyBtn;
  },

  // 购买按钮文案
  skuBuyBtnCommonText(state, getters) {
    const { isVirtual } = state.goods;
    const { hideShoppingCart } = state.shopConfig;
    const {
      isMultiBtn, isAddCart, isGift,
    } = state.skuConfig;

    if (isGift) {
      return '去付款';
    }

    if (isMultiBtn) {
      return isVirtual || hideShoppingCart ? '下一步' : getters.buttomBuyButtonText;
    }
    // 这里的文案在triggerEvent的时候，会拿去比较判断是否是加入购物车--注释勿删
    return isAddCart ? '加入购物车' : '下一步';
  },

  skuBuyBtnText(state, getters) {
    return getters.skuBuyBtnCommonText;
  },

  // sku商品详情
  skuGoodsDetail(state) {
    const {
      id, picture, alias, title, isVirtual = false, isVirtualTicket = false,
    } = state.goods || {};
    return {
      id,
      alias,
      title,
      picture: cdnImage(picture, '!300x300.jpg'),
      isVirtual,
      isVirtualTicket,
    };
  },

  skuPriceLabel(state, getters) {
    if (state.skuConfig.skuScene === SKU_SCENE.ACT_BUY) {
      return getters.activityLabel;
    }
    // 定金预售
    if (getters.isDepositPresale && !getters.isDepositPresaleOver) {
      return '定金';
    }
    return '';
  },

  skuCommonPriceTag(state) {
    const orderActivity = get(state, 'orderActivity', {});
    const discount = orderActivity.timelimitedDiscount || orderActivity.discount || {};
    return discount.title || '';
  },

  skuPrice(state, getters) {
    const { priceData = {}, selectedScale } = state.skuExtraData;

    if (getters.showladderGrouponExtra) {
      const ladderItem = getters.ladderGrouponSkuExtraList.filter(item => item.scale === selectedScale).pop();

      if (ladderItem) {
        return {
          oldPrice: priceData.showOldPrice,
          showPrice: '￥' + ladderItem.price,
          buyPrice: ladderItem.skuPrice,
        };
      }
      const lodderPrices = getters.ladderGrouponSkuExtraList.map(item => item.skuPrice);
      const minPrice = Math.min(...lodderPrices);
      const maxPrice = Math.max(...lodderPrices);

      return {
        oldPrice: priceData.showOldPrice,
        showPrice: `￥${money(minPrice).toYuan()}${minPrice === maxPrice ? '' : '起'}`,
      };
    }

    return priceData;
  },

  skuPriceTag(state, getters) {
    if (getters.saasIsSaasSku) {
      return getters.saasSkuPriceTag;
    }
    return getters.skuCommonPriceTag;
  },

  /* ============ 限购 ================ */
  // 由于多人拼团、积分商品包含两种限购状态
  // 电子卡券又有默认限购100件的隐藏逻辑，所以抽取通用限购逻辑

  // 多个活动商品存在不同的限购文案， 用一个通用的减少其他地方的判断
  quotaText(state, getters) {
    const { isHotel } = state;
    const { isVirtualTicket } = state.goods;
    const {
      quotaCycle, startSaleNum, quota, quotaUsed, isPointsBuy
    } = getters;
    return getQuotaText({
      quota,
      quotaUsed,
      quotaPeriod: quotaCycle,
      isHotel,
      isPoints: isPointsBuy,
      isVirtualTicket,
      // isHaitao,
      startSaleNum,
    });
  },

  commonQuota(state) {
    const timelimitedDiscount = get(state, 'orderActivity.timelimitedDiscount', null);
    if (timelimitedDiscount) {
      return timelimitedDiscount.limit.num;
    }
    return state.limit.num;
  },

  // 商品限购周期 0 终身 1 天 2 周 3 月
  commonQuotaPeriod(state) {
    const timelimitedDiscount = get(state, 'orderActivity.timelimitedDiscount', null);
    if (timelimitedDiscount) {
      return 0;
    }
    return state.limit.period || 0;
  },

  quota(state, getters) {
    const { goods = {} } = state;
    const { isVirtualTicket } = goods;
    const { commonQuota, quotaUsed, isHelpDepositPresale } = getters;

    if (isVirtualTicket) {
      // 电子卡券由于上限100件的限制，要手动维护quota
      return commonQuota ? Math.min(commonQuota, 100 + quotaUsed) : 100 + quotaUsed;
    }

    if (isHelpDepositPresale) {
      return 1;
    }

    return commonQuota ? Math.max(commonQuota, 1) : 0;
  },

  quotaUsed(state) {
    const timelimitedDiscount = get(state, 'orderActivity.timelimitedDiscount', null);
    if (timelimitedDiscount) {
      return timelimitedDiscount.limit.used;
    }
    return state.limit.used;
  },

  showladderGrouponExtra(state, getters) {
    return getters.isLadderGroupOn && state.skuConfig.skuScene === SKU_SCENE.ACT_BUY;
  },

  quotaCycle(state, getters) {
    return getters.commonQuotaPeriod;
  },

  startSaleNum(state) {
    return state.limit.startSaleNum;
  },

  /* ============ 虚拟商品价格日历 =============== */
  // 是否支持价格日历
  isPriceCalendar(state) {
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    return virtualTicket.validityType === 3;
  },

  // 价格日历需要默认选择sku
  initialSku(state,) {
    const { sku } = state;
    const _initialSku = {};

    // 电子卡券价格日历才设置初始sku
    if (
      state.goods.isVirtualTicket
      && state.goodsActivity.virtualTicket
      && state.goodsActivity.virtualTicket.validityType === 3
    ) {
      (sku.tree || []).forEach(treeItem => {
        _initialSku[treeItem.kS] = treeItem.v[0].id;
      });
    }

    return _initialSku;
  },

  // 默认sku对应的skuId，用于诸如价格日历
  initialSkuId(state, getters) {
    const { sku } = state;
    const { initialSku } = getters;
    if (!sku.list.length) {
      return sku.collectionId;
    }
    return (sku.list || []).find(item => {
      return everyValue(initialSku, (value, key) => item[key] === value);
    }).id;
  },
  // virtual end
};
