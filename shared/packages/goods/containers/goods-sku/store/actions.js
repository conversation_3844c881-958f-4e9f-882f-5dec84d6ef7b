import get from '@youzan/weapp-utils/lib/get';
import { SKU_SCENE, SKU_CONFIG_MAP } from 'shared/packages/goods/common/constant';
import {
  getGoodsListDate,
} from '../../../store/utils/fetch';

export default {
  stepperChange({ dispatch, commit, getters }, e) {
    const value = e.detail;

    commit('setSkuConfig', {
      stepperCount: value || 1
    });

    getters.showInstallmentSku && dispatch('calcInstallmentPrices', value);
  },
  // 点击showSkuPop，根据场景更新sku
  updateSkuConfig({ commit }, event) {
    const { skuScene = '' } = event;
    let skuType = 'buy';
    if (skuScene === SKU_SCENE.POINTS_BUY) {
      // 积分商品兑换
      skuType = 'point';
    } else if (skuScene === SKU_SCENE.GIFT_BUY) {
      // 送礼商品
      skuType = 'gift';
    } else if (skuScene === SKU_SCENE.SEL_SKU) {
      // 弹出sku选择窗，可选加入购物车或者购买商品
      skuType = 'selectSku';
    } else if (skuScene === SKU_SCENE.ADD_CART) {
      // 场景为添加购物车
      skuType = 'addCart';
    } else {
      // 普通商品购买
      // groupBuy presentBuy
      skuType = 'buy';
    }
    commit('setSkuConfig', {
      ...SKU_CONFIG_MAP.reset,
      ...SKU_CONFIG_MAP[skuType],
      skuScene
    });
  },

  updateSkuExtraData({ state, getters, commit }, event = {}) {
    let activityInfo = state.activityInfo;
    let priceData = {
      showPrice: getters.skuData.price,
      showOldPrice: getters.skuData.oldPrice,
    };
    const { skuScene = '' } = event;
    if (skuScene === SKU_SCENE.ACT_BUY) {
      // 团购 拼团 秒杀
    } else if (skuScene === SKU_SCENE.GIFT_BUY) {
      // 送礼
    } else if (skuScene === SKU_SCENE.PRESENT_BUY) {
      // 赠品
      priceData = {
        showPrice: 0,
        showOldPrice: '',
      };
    } else if (skuScene === SKU_SCENE.POINTS_BUY) {
      // 积分商品兑换
      priceData = {
        showPrice: getters.skuData.pointsPrice,
        showOldPrice: getters.skuData.price,
      };
    } else {
      // buy addCart selSku
      activityInfo = {
        activityAlias: '',
        activityId: 0,
        activityType: 0,
      };
    }
    const extraData = {
      activityInfo,
    };
    if (!state.skuExtraData.selectedSkuComb) {
      extraData.priceData = priceData;
    }
    commit('setSkuExtraData', extraData);
  },

  // virtual 电子卡券价格日历获取
  fetchListByDate({ state, getters, commit }, skuId) {
    const itemSkuId = skuId || getters.initialSkuId || 0;
    const virtualTicket = get(state, 'goodsActivity.virtualTicket', {});
    const { goods, activityInfo } = state;
    const query = {
      activityType: activityInfo.activityType,
      activityAlias: activityInfo.activityAlias,
      itemId: goods.id,
      itemSkuId,
    };

    // 设置了可预定时间
    if (virtualTicket.itemPreOrderTime) {
      Object.assign(query, {
        itemPreOrderTime: virtualTicket.itemPreOrderTime
      });
    }

    getGoodsListDate(query).then((res) => {
      if (res) {
        commit('setCalendarInfoData', res);
      }
    }).catch(err => {
      console.log(err.msg);
    });
  },
};
