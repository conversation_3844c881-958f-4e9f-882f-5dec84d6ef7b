export default {
  // 设置选择的sku
  setSkuExtraData(state, options = {}) {
    const {
      isPoints,
      activityInfo,
      selectedSku,
      selectedSkuComb,
      selectedSkuDate,
      priceData,
      selectedScale,
      selectedProp,
    } = options;
    if (activityInfo) {
      state.skuExtraData.activityInfo = activityInfo;
    }
    if (typeof priceData !== 'undefined') {
      let { showPrice, showOldPrice } = priceData;
      if (typeof showPrice === 'number') {
        showPrice = (showPrice / 100).toFixed(2, 10);
      }
      if (typeof showOldPrice === 'number') {
        showOldPrice = (showOldPrice / 100).toFixed(2, 10);
      }
      if (!isPoints) {
        showPrice = `￥${showPrice}`;
      }
      state.skuExtraData.priceData = {
        showPrice,
        showOldPrice,
      };
    }
    if (typeof selectedSku !== 'undefined') {
      state.skuExtraData.selectedSku = selectedSku;
    }
    if (typeof selectedProp !== 'undefined') {
      state.skuExtraData.selectedProp = selectedProp;
    }
    if (typeof selectedSkuComb !== 'undefined') {
      state.skuExtraData.selectedSkuComb = selectedSkuComb;
    }
    if (typeof selectedSkuDate !== 'undefined') {
      state.skuExtraData.selectedSkuDate = selectedSkuDate;
    }

    if (selectedScale) {
      state.skuExtraData.selectedScale = selectedScale;
    }
  },
};
