import get from '@youzan/weapp-utils/lib/get';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapMutations, mapGetters, mapActions } from '@youzan/vanx';
import navigate from 'shared/utils/navigate';
import { SKU_SCENE } from 'shared/packages/goods/common/constant';
import { skuTradeUtilsPromise } from 'shared/utils/async-base-sku';

const app = getApp();

VanxComponent({
  mapData: {
    ...mapGetters([
      'skuData',
      // 一些商品基础信息
      'skuGoodsDetail',
      // sku按钮文案，对应原来的buyText
      'skuBuyBtnText',
      // 是否显示立即买入按钮
      'skuShowBuyBtn',
      // 是否显示购物车按钮
      'skuShowAddCartBtn',
      // 限购数量
      'quota',
      // 已使用限购数量
      'quotaUsed',
      // 限购周期
      'quotaCycle',
      // 限购文案
      'quotaText',
      // 积分单位
      'pointsUnit',
      // 是否是周期购
      'isPeriodbuy',
      'periodbuyActivity',
      // 初始化的sku
      'initialSku',
      'showladderGrouponExtra',
      'isLadderGroupOn',
      // 云定制额外信息
      'saasSkuExtensions',
      // 云定制的标
      'cloudOrderExt',
      'showInstallmentSku',
      // 起售数量
      'startSaleNum',
    ]),
    ...mapState({
      showExtraSkuGroup(state, getters) {
        return getters.showladderGrouponExtra || getters.isPeriodbuy;
      },
      skuExtraData: (state) => state.skuExtraData,
      skuConfig: (state) => state.skuConfig,
      skuPopShow: (state) => state.displayPop.skuPopShow,
      isGroupOn: (state) =>
        state.skuConfig.skuScene === SKU_SCENE.ACT_BUY &&
        state.marketActivity.groupOn,
      isPoints: (state) =>
        state.skuConfig.skuScene === SKU_SCENE.POINTS_BUY &&
        state.marketActivity.pointsExchange,
      isAddCart: (state) => state.skuConfig.skuScene === SKU_SCENE.ADD_CART,
      isGift: (state) => state.skuConfig.skuScene === SKU_SCENE.GIFT_BUY,
      isVirtualTicket: (state) => state.goods.isVirtualTicket,
      virtualTicket: (state) => state.goodsActivity.virtualTicket,
      priceCalendarData: (state) => state.priceCalendarData,
      cartCount: (state) => state.cartInfo.count || 0,
      properties: (state) => state.goods.itemSalePropList || [],
    }),
    ...mapState({
      showExtraGroup(state, getters) {
        return (
          getters.isPeriodbuy ||
          getters.showInstallmentSku ||
          getters.showladderGrouponExtra
        ); // 分期支付
      },
    }),
  },

  computed: mapGetters(['skuPrice']),

  data: {
    themeClass: '',
  },

  methods: {
    ...mapMutations(['setSkuExtraData']),
    ...mapActions([
      'fetchListByDate',
      'fetchCartCount',
      'hideSkuPop',
      'fetchInstallentPrice',
      'stepperChange',
      'startCalculate',
    ]),

    handleAddCart(event) {
      const { detail } = event;
      const activityInfo = get(this.data, 'skuExtraData.activityInfo', {});

      const postData = {
        goodsId: detail.goodsId,
        skuId: detail.skuId,
        num: detail.num,
        price: detail.price,
        pointsPrice: detail.collectionPointsPrice || detail.pointsPrice || 0,
        remainPrice: detail.remainPrice || 0,
        channelId: detail.channelId || 0,
        ...activityInfo,
        orderFrom: 'goods',
        orderType: this.data.isGift ? 1 : 0,
        cartMessages: detail.cartMessages,
        appointmentTime: detail.selectedSkuDate,
        propertyIds: detail.selectedSkuComb.properties.reduce((acc, cur) => {
          (cur.v || []).forEach((it) => {
            acc.push(it.id);
          });
          return acc;
        }, []),
      };

      return this.hookEvent(postData);
    },

    hookEvent(postData) {
      const page = getCurrentPages().pop();
      if (page && page.beforeCartSubmitEvent) {
        return page
          .beforeCartSubmitEvent(postData)
          .then(() => {
            return this.addToCart(postData);
          })
          .catch(() => {
            return Promise.reject('Hook beforeCartSubmitEvent 事件失败');
          });
      }
      return this.addToCart(postData);
    },

    // 包装方法，用于多个活动
    addToCart(postData = {}) {
      wx.showLoading();
      return skuTradeUtilsPromise()
        .then(({ addToCart }) => {
          return addToCart(postData);
        })
        .then(() => {
          this.handleSkuClose();
          wx.hideLoading();
          if (this.data.isGift) {
            // 跳转到送礼购物车
            navigate.redirect({
              url: '/packages/gift/cart/index',
            });
          } else {
            wx.showToast({
              title: '成功加入购物车',
              icon: 'none',
              duration: 2000,
            });
            this.fetchCartCount();
            // 刷新购物车列表
            app.trigger('component:sku:cart', {
              type: 'add',
            });
          }
        })
        .catch(({ msg }) => {
          msg &&
            wx.showToast({
              title: msg,
              icon: 'none',
              duration: 2000,
            });
          wx.hideLoading();
        });
    },

    handleBuy(event) {
      const { detail } = event;
      const { isAddCart, isGift } = this.data;
      if (isAddCart) {
        this.handleAddCart(event);
        return;
      }
      if (isGift) {
        this.handleAddCart({
          detail: {
            ...detail,
            orderType: 1,
            channelId: 30,
          },
        });
        return;
      }

      // ?? 积分加金额时，后端数据有问题
      const activityInfo = get(this.data, 'skuExtraData.activityInfo', {});
      const postData = {
        goodsId: detail.goodsId,
        num: detail.num,
        skuId: detail.skuId,
        isPoints: this.data.isPoints ? 1 : 0,
        price: detail.price,
        pointsPrice: detail.collectionPointsPrice || detail.pointsPrice || 0,
        remainPrice: detail.remainPrice || 0,
        // ?? 怎么处理
        qr: '',
        useWxpay: 0,
        orderFrom: '',
        orderType: this.data.isGift ? 1 : 0,
        ...activityInfo,
        messages: detail.messages,
        appointmentTime: detail.selectedSkuDate,
        extensions: this.data.saasSkuExtensions,
        cloudOrderExt: this.data.cloudOrderExt,
        propertyIds: detail.selectedSkuComb.properties.reduce((acc, cur) => {
          (cur.v || []).forEach((it) => {
            acc.push(it.id);
          });
          return acc;
        }, []),
      };

      if (this.data.isPeriodbuy) {
        const deliverTimeId = this.data.periodbuyActivity.deliverTimeId;
        if (typeof deliverTimeId !== 'number') {
          wx.showToast({
            title: '请选择周期购送达时间',
            icon: 'none',
          });
          return;
        }
        postData.deliverTime = deliverTimeId;
      }

      if (this.data.isLadderGroupOn && this.data.isGroupOn) {
        const selectedScale = get(this.data, 'skuExtraData.selectedScale');
        if (!selectedScale) {
          wx.showToast({
            title: '请选择拼团类型',
            icon: 'none',
          });
          return;
        }

        postData.price = this.skuPrice.buyPrice;
        postData.umpStepPerson = selectedScale;
      }

      if (this.data.showInstallmentSku) {
        const { installmentRate, selectedInstallmentPeriod } =
          this.data.skuConfig;

        Object.assign(postData, {
          isInstallment: true,
          installmentRate, // 费率
          selectedInstallmentPeriod, // 分期数
        });
      }
      return this.goToBuy(postData);
    },

    // 包装方法，用于多个活动
    goToBuy(postData = {}) {
      return skuTradeUtilsPromise().then(({ goToBuySingle }) => {
        return goToBuySingle({
          ...postData,
        });
      });
    },

    /* ===================== 组件内部交互 ================ */
    // sku组件更改sku触发获取新的价格日历信息
    // 诸如电子卡券
    handleSkuSelect(event) {
      if (event.detail) {
        const { type, selectedSku, selectedSkuComb, selectedSkuDate } =
          event.detail;
        if (
          this.data.isVirtualTicket &&
          this.data.virtualTicket.validityType === 3
        ) {
          // 组合sku
          if (!selectedSkuComb || !selectedSkuComb.id) {
            return;
          }
          // ?? 有价格日历时，未选择价格日历，后端sku价格不准
          if (selectedSkuComb && type === 'item-select') {
            this.fetchListByDate(selectedSkuComb.id);
          }
        }

        // 分期购买
        if (this.data.showInstallmentSku) {
          this.fetchInstallentPrice(selectedSkuComb);
        }

        const { isPoints, pointsUnit } = this.data;

        const skuExtraData = {
          isPoints,
          selectedSku,
          selectedSkuComb,
          selectedSkuDate,
          skuId: selectedSkuComb ? selectedSkuComb.id : null,
        };

        if (isPoints) {
          const {
            oldPrice = null,
            pointsPrice = 0,
            pointsPriceStr = '',
            remainPrice = 0,
          } = selectedSkuComb || this.data.skuData;
          let showPrice = pointsPrice;
          // ?? 积分加金额显示时，后端逻辑有问题
          if (pointsPriceStr) {
            showPrice = pointsPriceStr;
          } else if (typeof pointsPrice === 'number') {
            showPrice = `${pointsPrice}${pointsUnit}`;
            if (+remainPrice > 0) {
              showPrice = `${showPrice} + ￥${(remainPrice / 100).toFixed(2)}`;
            }
          }
          skuExtraData.priceData = {
            showPrice,
            showOldPrice: oldPrice,
          };
        }
        this.setSkuExtraData(skuExtraData);

        if (!isPoints) {
          this.startCalculate();
        }
      }
    },

    handlePropSelect(event) {
      if (event.detail) {
        const { selectedProp, selectedSkuComb } = event.detail;
        const skuExtraData = {
          selectedProp,
          selectedSkuComb,
          skuId: selectedSkuComb ? selectedSkuComb.id : null,
        };
        this.setSkuExtraData(skuExtraData);
      }
      this.startCalculate();
    },

    /* ===================== 触发关闭sku浮层 ================== */
    handleSkuClose() {
      this.hideSkuPop();
    },
  },

  ready() {
    if (
      this.data.isVirtualTicket &&
      this.data.virtualTicket.validityType === 3
    ) {
      this.fetchListByDate();
    }
  },
});
