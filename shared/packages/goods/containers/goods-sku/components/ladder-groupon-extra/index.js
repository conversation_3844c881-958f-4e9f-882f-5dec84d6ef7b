import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapMutations } from '@youzan/vanx';

VanxComponent({
  name: 'ladder-groupon-extra',

  getters: ['ladderGrouponSkuExtraList'],

  state: ['skuExtraData'],

  methods: {
    ...mapMutations(['setSkuExtraData']),
    selectSkuItem({ currentTarget }) {
      const { selectedScale } = this.data.skuExtraData;
      const { scale } = currentTarget.dataset;
      this.setSkuExtraData({ selectedScale: selectedScale === scale ? null : scale });
    },
  },
});
