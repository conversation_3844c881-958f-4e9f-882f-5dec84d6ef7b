<view class="ladder-extra-group van-sku-group-container">
  <view class="ladder-extra-label van-sku-row__title">拼团类型：</view>
  <view class="ladder-extra-items">
    <theme-view
        wx:for="{{ ladderGrouponSkuExtraList }}"
        wx:key="{{ item.scale }}"
        data-scale="{{ item.scale }}"
        custom-class="van-sku-row__item"
        bg="{{ item.scale === skuExtraData.selectedScale ? 'main-bg' : '' }}"
        border="{{ item.scale === skuExtraData.selectedScale ? 'main-bg' : '' }}"
        color="{{ item.scale === skuExtraData.selectedScale ? 'main-text' : '' }}"
        bindtap="selectSkuItem"
      >
        <view class="van-sku-row__item-name">￥{{ item.price }}/{{ item.scale }}人团</view>
    </theme-view>
  </view>
</view>
