
<view class="van-sku-row">
  <view class="van-sku-row__title">
    送达时间：
    <view class="van-sku-row__extra-text theme__color">{{ periodbuyDeliverTimeStr }}</view>
  </view>
  <view class="van-sku-row__body">
    <theme-view
      wx:for="{{ periodbuySkuExtraList }}"
      wx:key="{{ item.id }}"
      data-item-id="{{ item.id }}"
      custom-class="van-sku-row__item"
      bg="{{ item.id === periodbuyActivity.deliverTimeId ? 'main-bg' : '' }}"
      border="{{ item.id === periodbuyActivity.deliverTimeId ? 'main-bg' : '' }}"
      color="{{ item.id === periodbuyActivity.deliverTimeId ? 'main-text' : '' }}"
      bindtap="selectSkuItem"
    >
      <view class="van-sku-row__item-name">{{ item.name }}</view>
    </theme-view>
  </view>
</view>
