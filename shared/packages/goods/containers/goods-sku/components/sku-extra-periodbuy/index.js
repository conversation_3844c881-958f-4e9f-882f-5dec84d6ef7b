import theme from 'shared/common/components/theme-view/theme';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapGetters, mapActions } from '@youzan/vanx';

VanxComponent({
  mapData: {
    ...mapGetters([
      'periodbuyActivity',
      'periodbuySkuExtraList',
      'periodbuyDeliverTimeStr',
    ]),
  },

  data: {
    themeGeneral: '',
  },

  methods: {
    ...mapActions(['selectPeriodbuySkuItem']),
    selectSkuItem(_event) {
      const { itemId } = _event.currentTarget.dataset;
      this.selectPeriodbuySkuItem(itemId);
    }
  },

  ready() {
    theme.getThemeColor('general').then(color => {
      this.setYZData({
        themeGeneral: color
      });
    });
  },
});
