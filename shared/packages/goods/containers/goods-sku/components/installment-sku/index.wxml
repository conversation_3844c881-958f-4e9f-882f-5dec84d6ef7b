<view class="sku-installment">
  <view class="installment-title">
    <view>分期支付<text>(可选)</text></view>
    <view class="tip">分期金额、服务费以收银台展示为准，小于10元不可分期</view>
  </view>
  <view class="installment-list">
    <view
      data-key="{{ 1 }}"
      class="installment-item {{ hasSkuId ? '' : 'disabled' }} {{ 1 === skuConfig.selectedInstallmentPeriod ? 'active' : '' }}"
      bind:tap="setCount"
    >
      <view>分1期</view>
      <view>先用后付，无服务费</view>
    </view>

    <view
      wx:for="{{ installment }}"
      wx:key="{{ item.key }}"
      class="installment-item {{ (!hasSkuId || noInstallmentOrder) ? 'disabled' : '' }} {{ item.key == skuConfig.selectedInstallmentPeriod ? 'active' : '' }}"
      data-key="{{ item.key }}"
      bind:tap="setCount"
    >
      <view>分 {{ item.key }} 期（含手续费）</view>
      <view>¥{{ item.val }}/期</view>
    </view>
  </view>
</view>
