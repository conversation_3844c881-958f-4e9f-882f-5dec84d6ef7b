import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapMutations, mapState } from '@youzan/vanx';

VanxComponent({
  name: 'installment',

  mapData: {
    ...mapState(['skuConfig', 'installment', 'noInstallmentOrder']),
    ...mapState({
      hasSkuId(state, getters) {
        return (
          state.skuExtraData.selectedSkuComb
            && !!state.skuExtraData.selectedSkuComb.id
        )
        || (
          getters.skuData
          && getters.skuData.noneSku
        );
      }
    })
  },

  methods: {
    ...mapMutations(['SET_INSTALLMENT_COUNT']),
    setCount(e) {
      this.SET_INSTALLMENT_COUNT(+e.currentTarget.dataset.key);
    }
  }
});

