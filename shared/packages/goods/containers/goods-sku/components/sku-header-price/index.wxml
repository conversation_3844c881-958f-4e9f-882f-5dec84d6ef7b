<block>
  <theme-view
    color="gerneral"
    class="van-sku__goods-price">
    <block wx:if="{{ skuPriceLabel }}">
      <text class="van-sku__price-label">{{ skuPriceLabel }} </text>
      <text class="van-sku__price-num">{{ skuPrice.showPrice }}</text>
    </block>
    <block wx:else>
      <!-- <text class="van-sku__price-symbol"></text> -->
      <text class="van-sku__price-num">{{ skuPrice.showPrice }}</text>
    </block>
    <van-tag
      wx:if="{{ depositOffer || skuData.depositOffer }}"
      plain
      type="danger"
      class="van-sku__price-tag line-height--18"
      color="{{ themeGeneral }}"
      custom-class="goods-detail__tag--plain"
    >{{ selectedSkuDeposit ? selectedSkuDeposit : '可抵' + (depositOffer || skuData.depositOffer) }}</van-tag>
    <van-tag
      wx:if="{{ skuPriceTag }}"
      plain
      type="danger"
      class="van-sku__price-tag line-height--18"
      color="{{ themeGeneral }}"
      custom-class="goods-detail__tag--plain"
    >{{ skuPriceTag }}</van-tag>
  </theme-view>
  <view
    class="van-text-deleted van-font-12 van-c-gray van-sku__price-old"
    wx:if="{{ skuPrice.showOldPrice }}"
  > ￥ {{ skuPrice.showOldPrice }}</view>
</block>
