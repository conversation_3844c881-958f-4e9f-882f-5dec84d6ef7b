import theme from 'shared/common/components/theme-view/theme';
import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { mapState, mapGetters } from '@youzan/vanx';
import get from '@youzan/weapp-utils/lib/get';

VanxComponent({
  mapData: {
    ...mapGetters(['skuData', 'skuPriceTag', 'skuPriceLabel', 'selectedSkuDeposit', 'skuPrice']),
    ...mapState({
      depositOffer: (state) => get(state, 'skuExtraData.selectedSkuComb.depositOffer', ''),
    }),
  },

  data: {
    themeGeneral: '',
  },

  ready() {
    theme.getThemeColor('general').then(color => {
      this.setYZData({
        themeGeneral: color
      });
    });
  },
});
