import { countDownRemainParse } from '@youzan/weapp-utils/lib/time';
import { VanxComponent } from 'shared/common/base/wsc-component/index';

VanxComponent({
  properties: {
    soldTime: {
      type: Number,
      observer: 'updateCountDown'
    }
  },

  data: {
    // 标记是否经历过从开始到结束的过程，如果结束则刷新页面
    fromStartToEnd: false,
    remainStr: '',
    waitSoldTime: 0,
  },

  methods: {
    updateCountDown() {
      if (!this.data.soldTime) {
        return;
      }
      const currentTime = +new Date();
      this.setYZData({
        waitSoldTime: +this.data.soldTime + currentTime,
      });
      this.runCountDown();
    },
    runCountDown() {
      clearTimeout(this._timer);
      const { waitSoldTime, fromStartToEnd = false } = this.data;
      if (!waitSoldTime) {
        return;
      }
      const currentTime = +new Date();
      const remain = waitSoldTime - currentTime;

      if (remain <= 0) {
        if (fromStartToEnd) {
          setTimeout(() => {
            wx.startPullDownRefresh();
          }, 500);
        }
        return;
      }

      const remainStr = countDownRemainParse(remain).toDateString();

      this.setYZData({
        remainStr,
        fromStartToEnd: true,
      });
      clearTimeout(this._timer);
      this._timer = setTimeout(() => {
        this.runCountDown();
      }, 1000);
    }
  },

  attached() {
    this._timer = null;
  },

  detached() {
    clearTimeout(this.timer);
  }
});
