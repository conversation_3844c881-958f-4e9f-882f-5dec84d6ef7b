@import '~shared/common/css/_variables.scss';
@import '~shared/common/css/_mixins.scss';

.goods-detail__popup {
  .scroll-container {
    background: #fff;
    width: 100vw;
    overflow: hidden;
    left: -1px;
    position: relative;
  }

  .popup-title__view {
    margin: 12px 0;
    color: #111;
    font-size: 16px;
    text-align: center; 
  }

  scroll-view {
    height: 60vh;
  }

  button {
    position: flex;
    height: 50px;
    border-radius: 0;
    &::after {
      display: none;
    }
  }
}

.order-preference__btn {
  color: inherit;
  background-color: transparent;
}

.goods-detail__popup.iPhone-X {
  .scroll-container,
  button,
  .ipone-x-bottom {
    margin-left: -1px;
  }

  .ipone-x-bottom {
    @include iphone-x-after($iphone-x-bottom);
  }
}
