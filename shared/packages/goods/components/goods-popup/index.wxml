<van-popup
  position="bottom"
  show="{{ show }}"
  close-on-click-overlay
  bind:click-overlay="onTapBtn"
  class="goods-detail__popup {{ deviceType }}"
>
  <view class="scroll-container">
    <view class="popup-title__view" wx:if="{{ title }}">{{ title }}</view>
    <scroll-view scroll-y class="scroll-class">
      <slot />
    </scroll-view>
  </view>
  <theme-view bg="main-bg" color="main-text">
    <button class="order-preference__btn" bindtap="onTapBtn">我知道了</button>
  </theme-view>
  <view class="ipone-x-bottom" />
</van-popup>