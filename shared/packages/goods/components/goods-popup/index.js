/**
 * 被店铺信息(shop-info)所引用
 */

import { VanxComponent } from 'shared/common/base/wsc-component/index';
import { isNewIphone } from 'shared/utils/browser/device-type';

VanxComponent({
  properties: {
    show: <PERSON><PERSON><PERSON>,
    title: String,
    themeClass: String
  },

  data: {
    deviceType: '',
  },

  attached() {
    if (isNewIphone()) {
      this.setYZData({
        deviceType: 'iPhone-X',
      });
    }
  },

  externalClasses: ['scroll-class'],

  methods: {
    onTapBtn() {
      this.triggerEvent('tap:btn');
    }
  }
});
